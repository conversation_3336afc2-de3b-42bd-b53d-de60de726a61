package com.pokecobble;

import com.pokecobble.command.ScreenDebugCommand;
import com.pokecobble.command.TestCommand;
import com.pokecobble.network.ScreenNetworkHandler;
import com.pokecobble.performance.PerformanceOptimizer;
import com.pokecobble.registry.ModBlockEntities;
import com.pokecobble.registry.ModBlocks;
import com.pokecobble.registry.ModItemGroups;
import com.pokecobble.registry.ModScreenHandlers;
import com.pokecobble.web.ScreenWebServer;
import com.pokecobble.event.ServerLifecycleEvents;
import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ScreenBlock implements ModInitializer {
	public static final String MOD_ID = "screen-block";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	@Override
	public void onInitialize() {
		// This code runs as soon as Minecraft is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.

		LOGGER.info("Initialisation du mod Screen Block...");

		// Enregistrer les blocs
		ModBlocks.registerModBlocks();

		// Enregistrer les entités de blocs
		ModBlockEntities.registerBlockEntities();

		// Enregistrer les screen handlers
		ModScreenHandlers.registerScreenHandlers();

		// Enregistrer les groupes d'items (menu créatif)
		ModItemGroups.registerItemGroups();

		// Initialiser le gestionnaire de réseau côté serveur
		ScreenNetworkHandler.initServer();

		// Enregistrer les commandes de debug et de test
		CommandRegistrationCallback.EVENT.register((dispatcher, registryAccess, environment) -> {
			ScreenDebugCommand.register(dispatcher, registryAccess, environment);
			TestCommand.register(dispatcher, registryAccess);
		});

		// Enregistrer les événements de cycle de vie du serveur
		ServerLifecycleEvents.register();

		LOGGER.info("Mod Screen Block initialisé avec succès !");
	}
}