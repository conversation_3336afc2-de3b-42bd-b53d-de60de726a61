package com.pokecobble.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.performance.PerformanceOptimizer;
import com.pokecobble.test.AutomatedTests;
import com.pokecobble.test.SeamlessDistributionTest;
import com.pokecobble.ScreenBlock;
import com.pokecobble.debug.VerticalConnectionDebugger;
import com.pokecobble.screen.LargeStructureOptimizer;
import com.pokecobble.screen.RectangularStructureDetector;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.math.BlockPos;
import net.minecraft.command.argument.BlockPosArgumentType;

import java.util.List;

/**
 * Commande pour lancer les tests automatisés et afficher les statistiques
 */
public class TestCommand {

    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess) {
        dispatcher.register(CommandManager.literal("screen-test")
            .requires(source -> source.hasPermissionLevel(2))
            .then(CommandManager.literal("run")
                .executes(TestCommand::runTests))
            .then(CommandManager.literal("results")
                .executes(TestCommand::showResults))
            .then(CommandManager.literal("performance")
                .executes(TestCommand::showPerformance))
            .then(CommandManager.literal("debug-vertical")
                .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                    .executes(TestCommand::debugVerticalConnections)))
            .then(CommandManager.literal("test-vertical")
                .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                    .executes(TestCommand::testVerticalConnections)))
            .then(CommandManager.literal("test-large")
                .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                    .executes(TestCommand::testLargeStructure)))
            .then(CommandManager.literal("test-rectangle")
                .then(CommandManager.argument("pos", BlockPosArgumentType.blockPos())
                    .executes(TestCommand::testRectangularStructure)))
            .then(CommandManager.literal("optimization-stats")
                .executes(TestCommand::showOptimizationStats))
            .then(CommandManager.literal("test-seamless")
                .executes(TestCommand::testSeamlessDistribution))
            .then(CommandManager.literal("help")
                .executes(TestCommand::showHelp))
        );
    }

    /**
     * Lance tous les tests automatisés
     */
    private static int runTests(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("🧪 Lancement des tests automatisés...")
            .formatted(Formatting.YELLOW), false);

        // Lancer les tests de manière asynchrone
        AutomatedTests.runAllTests(source.getWorld()).thenAccept(results -> {
            // Envoyer les résultats
            source.sendFeedback(() -> Text.literal("🧪 Tests terminés!")
                .formatted(Formatting.GREEN), false);

            source.sendFeedback(() -> Text.literal(AutomatedTests.getTestSummary())
                .formatted(Formatting.AQUA), false);

            // Afficher les échecs s'il y en a
            for (AutomatedTests.TestResult result : results) {
                if (!result.passed) {
                    source.sendFeedback(() -> Text.literal("❌ " + result.testName + ": " + result.message)
                        .formatted(Formatting.RED), false);
                }
            }

            // Afficher les succès en mode verbose
            final int passedCount = (int) results.stream().filter(result -> result.passed).count();

            if (passedCount > 0) {
                source.sendFeedback(() -> Text.literal("✅ " + passedCount + " tests réussis")
                    .formatted(Formatting.GREEN), false);
            }
        }).exceptionally(throwable -> {
            source.sendFeedback(() -> Text.literal("❌ Erreur lors des tests: " + throwable.getMessage())
                .formatted(Formatting.RED), false);
            return null;
        });

        return 1;
    }

    /**
     * Affiche les résultats des derniers tests
     */
    private static int showResults(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        List<AutomatedTests.TestResult> results = AutomatedTests.getTestResults();

        if (results.isEmpty()) {
            source.sendFeedback(() -> Text.literal("Aucun test n'a été exécuté. Utilisez /screen-test run")
                .formatted(Formatting.YELLOW), false);
            return 0;
        }

        source.sendFeedback(() -> Text.literal("📊 Résultats des tests:")
            .formatted(Formatting.GOLD), false);

        source.sendFeedback(() -> Text.literal(AutomatedTests.getTestSummary())
            .formatted(Formatting.AQUA), false);

        // Afficher tous les résultats
        for (AutomatedTests.TestResult result : results) {
            Formatting color = result.passed ? Formatting.GREEN : Formatting.RED;
            String status = result.passed ? "✅" : "❌";

            source.sendFeedback(() -> Text.literal(String.format("%s [%dms] %s: %s",
                status, result.executionTime, result.testName, result.message))
                .formatted(color), false);
        }

        return 1;
    }

    /**
     * Affiche les statistiques de performance
     */
    private static int showPerformance(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("📈 Statistiques de performance:")
            .formatted(Formatting.GOLD), false);

        String stats = PerformanceOptimizer.getPerformanceStats();
        String[] lines = stats.split("\n");

        for (String line : lines) {
            source.sendFeedback(() -> Text.literal(line)
                .formatted(Formatting.WHITE), false);
        }

        // Informations système
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024; // MB
        long freeMemory = runtime.freeMemory() / 1024 / 1024; // MB
        long usedMemory = totalMemory - freeMemory;

        source.sendFeedback(() -> Text.literal(String.format("Mémoire: %d MB utilisés / %d MB total",
            usedMemory, totalMemory))
            .formatted(Formatting.AQUA), false);

        return 1;
    }

    /**
     * Affiche l'aide pour les commandes de test
     */
    private static int showHelp(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("🧪 Commandes de test Screen Block:")
            .formatted(Formatting.GOLD), false);

        source.sendFeedback(() -> Text.literal("/screen-test run - Lance tous les tests automatisés")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test results - Affiche les résultats des derniers tests")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test performance - Affiche les statistiques de performance")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test help - Affiche cette aide")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test debug-vertical <pos> - Diagnostique les connexions verticales")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test test-vertical <pos> - Teste les connexions verticales")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test test-large <pos> - Teste les grandes structures (3x3+)")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test test-rectangle <pos> - Teste la détection rectangulaire")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("/screen-test optimization-stats - Affiche les stats d'optimisation")
            .formatted(Formatting.WHITE), false);

        source.sendFeedback(() -> Text.literal("Note: Ces commandes nécessitent les permissions d'opérateur")
            .formatted(Formatting.GRAY), false);

        return 1;
    }

    /**
     * Diagnostique les connexions verticales à une position donnée
     */
    private static int debugVerticalConnections(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");

        VerticalConnectionDebugger.diagnoseVerticalConnections(source, pos);
        return 1;
    }

    /**
     * Teste spécifiquement les connexions verticales
     */
    private static int testVerticalConnections(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");

        VerticalConnectionDebugger.testVerticalConnections(source, pos);
        return 1;
    }

    /**
     * Teste la détection et l'optimisation des grandes structures
     */
    private static int testLargeStructure(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");

        source.sendFeedback(() -> Text.literal("§6=== TEST GRANDES STRUCTURES ==="), false);
        source.sendFeedback(() -> Text.literal("§7Position testée: " + pos), false);

        // Tester si une grande structure peut se former
        boolean canForm = RectangularStructureDetector.canFormLargeStructure(source.getWorld(), pos);
        source.sendFeedback(() -> Text.literal("§7Peut former une grande structure: " +
                                              (canForm ? "§a✅ OUI" : "§c❌ NON")), false);

        // Détecter la structure rectangulaire
        RectangularStructureDetector.RectangularStructure rectangular =
            RectangularStructureDetector.detectRectangularStructure(source.getWorld(), pos);

        if (rectangular != null) {
            source.sendFeedback(() -> Text.literal("§a✅ Structure rectangulaire détectée: " + rectangular), false);
            source.sendFeedback(() -> Text.literal("§7Type: " + rectangular.getType()), false);
            source.sendFeedback(() -> Text.literal("§7Grande: " + (rectangular.isLarge() ? "§aOUI" : "§cNON")), false);
            source.sendFeedback(() -> Text.literal("§7Massive: " + (rectangular.isMassive() ? "§aOUI" : "§cNON")), false);
        } else {
            source.sendFeedback(() -> Text.literal("§c❌ Aucune structure rectangulaire détectée"), false);
        }

        return 1;
    }

    /**
     * Teste spécifiquement la détection rectangulaire
     */
    private static int testRectangularStructure(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        BlockPos pos = BlockPosArgumentType.getBlockPos(context, "pos");

        source.sendFeedback(() -> Text.literal("§6=== TEST DÉTECTION RECTANGULAIRE ==="), false);

        // Tester les patterns communs
        RectangularStructureDetector.RectangularStructure common =
            RectangularStructureDetector.StructurePattern.detectCommonPattern(source.getWorld(), pos);

        if (common != null) {
            source.sendFeedback(() -> Text.literal("§a✅ Pattern commun détecté: " + common), false);
        } else {
            source.sendFeedback(() -> Text.literal("§c❌ Aucun pattern commun détecté"), false);
        }

        // Détecter la plus grande structure possible
        RectangularStructureDetector.RectangularStructure largest =
            RectangularStructureDetector.detectLargestRectangle(source.getWorld(), pos);

        if (largest != null) {
            source.sendFeedback(() -> Text.literal("§a✅ Plus grande structure: " + largest), false);
        } else {
            source.sendFeedback(() -> Text.literal("§c❌ Aucune grande structure détectée"), false);
        }

        // Forcer la détection jusqu'à 10x10
        RectangularStructureDetector.RectangularStructure forced =
            RectangularStructureDetector.forceRectangularDetection(source.getWorld(), pos, 10);

        if (forced != null) {
            source.sendFeedback(() -> Text.literal("§a✅ Détection forcée: " + forced), false);
        } else {
            source.sendFeedback(() -> Text.literal("§c❌ Détection forcée échouée"), false);
        }

        return 1;
    }

    /**
     * Affiche les statistiques d'optimisation
     */
    private static int showOptimizationStats(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("§6=== STATISTIQUES D'OPTIMISATION ==="), false);

        // Stats de l'optimiseur de grandes structures
        String optimizationStats = LargeStructureOptimizer.getOptimizationStats();
        String[] lines = optimizationStats.split("\n");

        for (String line : lines) {
            source.sendFeedback(() -> Text.literal("§7" + line), false);
        }

        return 1;
    }

    /**
     * Teste la distribution seamless de contenu
     */
    private static int testSeamlessDistribution(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("🎨 Test de distribution seamless...")
            .formatted(Formatting.YELLOW), false);

        try {
            // Lancer les tests de distribution seamless
            SeamlessDistributionTest.runAllTests();

            source.sendFeedback(() -> Text.literal("✅ Tests de distribution seamless terminés avec succès!")
                .formatted(Formatting.GREEN), false);

            source.sendFeedback(() -> Text.literal("📊 Vérifiez les logs pour les détails des tests")
                .formatted(Formatting.AQUA), false);

        } catch (Exception e) {
            source.sendFeedback(() -> Text.literal("❌ Erreur lors des tests: " + e.getMessage())
                .formatted(Formatting.RED), false);
            ScreenBlock.LOGGER.error("Erreur tests seamless", e);
        }

        return 1;
    }
}
