package com.pokecobble.event;

import com.pokecobble.ScreenBlock;
import com.pokecobble.web.ScreenWebServer;
import net.minecraft.server.MinecraftServer;

/**
 * Gestionnaire des événements de cycle de vie du serveur
 */
public class ServerLifecycleEvents {

    public static void register() {
        // Démarrer le serveur web quand le serveur Minecraft est prêt
        net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents.SERVER_STARTED.register(server -> {
            ScreenBlock.LOGGER.info("Serveur Minecraft démarré, initialisation du serveur web...");
            ScreenWebServer.setMinecraftServer(server);
            ScreenWebServer.start();
        });

        // Arrêter le serveur web quand le serveur Minecraft s'arrête
        net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents.SERVER_STOPPING.register(server -> {
            ScreenBlock.LOGGER.info("Arrêt du serveur web...");
            ScreenWebServer.stop();
        });
    }
}
