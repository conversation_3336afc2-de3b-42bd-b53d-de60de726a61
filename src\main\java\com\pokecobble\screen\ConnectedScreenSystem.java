package com.pokecobble.screen;

import com.pokecobble.ScreenBlock;
import com.pokecobble.util.ScreenLogger;
import com.pokecobble.block.ScreenDisplayBlock;
import com.pokecobble.block.entity.ScreenDisplayBlockEntity;
import com.pokecobble.data.ScreenContent;
import com.pokecobble.registry.ModBlocks;
import net.minecraft.block.BlockState;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.world.World;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Système principal de gestion des écrans connectés
 * Recodage complet pour supporter parfaitement les connexions horizontales et verticales
 */
public class ConnectedScreenSystem {

    // Cache des structures d'écrans connectés
    private static final Map<BlockPos, ConnectedScreenStructure> structureCache = new ConcurrentHashMap<>();

    // Cache des connexions par bloc
    private static final Map<BlockPos, Set<Direction>> connectionCache = new ConcurrentHashMap<>();

    /**
     * Structure représentant un groupe d'écrans connectés
     */
    public static class ConnectedScreenStructure {
        private final Set<BlockPos> blocks;
        private final BlockPos masterBlock;
        private final int width, height, depth;
        private final World world;
        private ScreenContent sharedContent;

        public ConnectedScreenStructure(World world, Set<BlockPos> blocks) {
            this.world = world;
            this.blocks = new HashSet<>(blocks);

            // Calculer les dimensions et le bloc maître
            int minX = blocks.stream().mapToInt(pos -> pos.getX()).min().orElse(0);
            int maxX = blocks.stream().mapToInt(pos -> pos.getX()).max().orElse(0);
            int minY = blocks.stream().mapToInt(pos -> pos.getY()).min().orElse(0);
            int maxY = blocks.stream().mapToInt(pos -> pos.getY()).max().orElse(0);
            int minZ = blocks.stream().mapToInt(pos -> pos.getZ()).min().orElse(0);
            int maxZ = blocks.stream().mapToInt(pos -> pos.getZ()).max().orElse(0);

            this.width = maxX - minX + 1;
            this.height = maxZ - minZ + 1;
            this.depth = maxY - minY + 1;
            this.masterBlock = new BlockPos(minX, minY, minZ);

            ScreenBlock.LOGGER.info("Structure créée: " + width + "x" + height + "x" + depth +
                                   " (" + blocks.size() + " blocs)");
        }

        // Getters
        public Set<BlockPos> getBlocks() { return new HashSet<>(blocks); }
        public BlockPos getMasterBlock() { return masterBlock; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
        public int getDepth() { return depth; }
        public World getWorld() { return world; }

        /**
         * Calcule la position relative d'un bloc dans la structure
         */
        public RelativePosition getRelativePosition(BlockPos pos) {
            return new RelativePosition(
                pos.getX() - masterBlock.getX(),
                pos.getY() - masterBlock.getY(),
                pos.getZ() - masterBlock.getZ()
            );
        }

        /**
         * Met à jour le contenu partagé et le distribue sur tous les blocs
         */
        public void updateSharedContent(ScreenContent content) {
            this.sharedContent = content;

            // Utiliser le nouveau système dynamique
            com.pokecobble.display.DynamicScreenDisplaySystem.updateDynamicScreenContent(world, masterBlock, content);
        }

        /**
         * Distribue le contenu sur tous les blocs de la structure
         */
        private void distributeContent() {
            if (sharedContent == null) return;

            for (BlockPos pos : blocks) {
                if (world.getBlockEntity(pos) instanceof ScreenDisplayBlockEntity entity) {
                    RelativePosition relPos = getRelativePosition(pos);

                    // Créer le contenu spécifique à ce bloc
                    ScreenContent blockContent = ScreenContentDistributor.distributeContent(
                        sharedContent, relPos, width, height, depth
                    );

                    // Appliquer le contenu sans propager (éviter la récursion)
                    entity.setContent(blockContent, false);
                }
            }

            ScreenBlock.LOGGER.info("Contenu distribué sur " + blocks.size() + " blocs");
        }

        /**
         * Vérifie si la structure est toujours valide
         */
        public boolean isValid() {
            for (BlockPos pos : blocks) {
                if (!world.getBlockState(pos).isOf(ModBlocks.SCREEN_DISPLAY_BLOCK)) {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * Position relative dans une structure 3D
     */
    public static class RelativePosition {
        public final int x, y, z;

        public RelativePosition(int x, int y, int z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        @Override
        public String toString() {
            return "(" + x + "," + y + "," + z + ")";
        }
    }

    /**
     * Appelé quand un bloc d'écran est placé
     */
    public static void onScreenBlockPlaced(World world, BlockPos pos) {
        if (world.isClient) return;

        // Nettoyer les anciens caches
        invalidateCache(pos);

        // Détecter les connexions
        updateConnections(world, pos);

        // Détecter la structure
        ConnectedScreenStructure structure = detectStructure(world, pos);

        if (structure != null && structure.getBlocks().size() > 1) {
            // Optimiser si c'est une grande structure
            LargeStructureOptimizer.optimizeIfNeeded(structure);

            // Enregistrer la structure pour tous ses blocs
            for (BlockPos blockPos : structure.getBlocks()) {
                structureCache.put(blockPos, structure);
            }

            // Synchroniser le contenu existant
            synchronizeStructureContent(structure);
        }
    }

    /**
     * Appelé quand un bloc d'écran est supprimé
     */
    public static void onScreenBlockRemoved(World world, BlockPos pos) {
        if (world.isClient) return;

        // Récupérer l'ancienne structure
        ConnectedScreenStructure oldStructure = structureCache.get(pos);

        // Nettoyer les caches
        invalidateCache(pos);

        if (oldStructure != null) {
            // Nettoyer l'optimisation si elle existe
            LargeStructureOptimizer.cleanupOptimizedStructure(oldStructure.getMasterBlock());

            // Nettoyer la structure pour tous ses blocs
            for (BlockPos blockPos : oldStructure.getBlocks()) {
                structureCache.remove(blockPos);
                connectionCache.remove(blockPos);
            }

            // Re-détecter les structures pour les blocs restants
            for (BlockPos blockPos : oldStructure.getBlocks()) {
                if (!blockPos.equals(pos) && world.getBlockState(blockPos).isOf(ModBlocks.SCREEN_DISPLAY_BLOCK)) {
                    onScreenBlockPlaced(world, blockPos);
                }
            }
        }
    }

    /**
     * Met à jour les connexions d'un bloc
     */
    public static void updateConnections(World world, BlockPos pos) {
        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Mise à jour des connexions pour position %s", pos);

        BlockState state = world.getBlockState(pos);
        if (!state.isOf(ModBlocks.SCREEN_DISPLAY_BLOCK)) {
            ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
                "Position %s n'est pas un bloc d'écran - ignoré", pos);
            return;
        }

        Set<Direction> connections = ScreenConnectionDetector.detectConnections(world, pos);
        connectionCache.put(pos, connections);

        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Connexions détectées pour %s: %s", pos, connections);

        // Mettre à jour les propriétés du bloc
        BlockState newState = state;
        for (Direction dir : Direction.values()) {
            boolean connected = connections.contains(dir);
            newState = newState.with(getConnectionProperty(dir), connected);
        }

        if (!newState.equals(state)) {
            world.setBlockState(pos, newState, 3);
        }
    }

    /**
     * Détecte une structure d'écrans connectés avec optimisation pour les rectangles
     */
    public static ConnectedScreenStructure detectStructure(World world, BlockPos startPos) {
        long startTime = ScreenLogger.startTimer();
        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Début détection de structure à la position %s", startPos);

        // Essayer d'abord la détection rectangulaire pour les grandes structures
        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Tentative de détection rectangulaire...");
        RectangularStructureDetector.RectangularStructure rectangular =
            RectangularStructureDetector.detectRectangularStructure(world, startPos);

        if (rectangular != null && rectangular.isLarge()) {
            ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
                "Structure rectangulaire détectée: %s", rectangular);
            ConnectedScreenStructure structure = new ConnectedScreenStructure(world, rectangular.getBlocks());

            long elapsedTime = ScreenLogger.elapsedTime(startTime);
            ScreenLogger.info(ScreenLogger.CATEGORY_CONNECTION,
                "Structure rectangulaire créée en %dms - %d blocs connectés",
                elapsedTime, rectangular.getBlocks().size());

            return structure;
        }

        // Fallback vers la détection normale
        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Utilisation de la détection flood-fill classique...");
        Set<BlockPos> connectedBlocks = ScreenConnectionDetector.findConnectedBlocks(world, startPos);

        if (connectedBlocks.size() <= 1) {
            ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
                "Aucune structure détectée - seulement %d bloc(s) trouvé(s)", connectedBlocks.size());
            return null;
        }

        ConnectedScreenStructure structure = new ConnectedScreenStructure(world, connectedBlocks);
        long elapsedTime = ScreenLogger.elapsedTime(startTime);
        ScreenLogger.info(ScreenLogger.CATEGORY_CONNECTION,
            "Structure flood-fill créée en %dms - %d blocs connectés",
            elapsedTime, connectedBlocks.size());

        return structure;
    }

    /**
     * Met à jour le contenu d'une structure
     */
    public static void updateStructureContent(World world, BlockPos pos, ScreenContent content) {
        ConnectedScreenStructure structure = structureCache.get(pos);
        if (structure != null) {
            structure.updateSharedContent(content);
        }
    }

    /**
     * Vérifie si un bloc fait partie d'une structure
     */
    public static boolean isPartOfStructure(World world, BlockPos pos) {
        return structureCache.containsKey(pos);
    }

    /**
     * Obtient la structure d'un bloc
     */
    public static ConnectedScreenStructure getStructure(BlockPos pos) {
        return structureCache.get(pos);
    }

    // Méthodes utilitaires privées

    private static void invalidateCache(BlockPos pos) {
        structureCache.remove(pos);
        connectionCache.remove(pos);
    }

    private static void synchronizeStructureContent(ConnectedScreenStructure structure) {
        // Trouver un bloc avec du contenu existant
        ScreenContent existingContent = null;
        for (BlockPos pos : structure.getBlocks()) {
            if (structure.getWorld().getBlockEntity(pos) instanceof ScreenDisplayBlockEntity entity) {
                ScreenContent content = entity.getContentDirect();
                if (content != null && (!content.getDisplayText().isEmpty() || content.hasImageData())) {
                    existingContent = content;
                    break;
                }
            }
        }

        if (existingContent != null) {
            structure.updateSharedContent(existingContent);
        }
    }

    private static net.minecraft.state.property.BooleanProperty getConnectionProperty(Direction direction) {
        return switch (direction) {
            case NORTH -> ScreenDisplayBlock.CONNECTED_NORTH;
            case SOUTH -> ScreenDisplayBlock.CONNECTED_SOUTH;
            case EAST -> ScreenDisplayBlock.CONNECTED_EAST;
            case WEST -> ScreenDisplayBlock.CONNECTED_WEST;
            case UP -> ScreenDisplayBlock.CONNECTED_UP;
            case DOWN -> ScreenDisplayBlock.CONNECTED_DOWN;
        };
    }
}
