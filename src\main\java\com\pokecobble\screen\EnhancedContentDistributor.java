package com.pokecobble.screen;

import com.pokecobble.data.ScreenContent;
import com.pokecobble.ScreenBlock;
import com.pokecobble.screen.ConnectedScreenSystem.RelativePosition;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * Distributeur de contenu amélioré avec interpolation avancée et lissage seamless
 * Améliore la qualité visuelle pour les grandes structures d'écrans connectés
 */
public class EnhancedContentDistributor {
    
    /**
     * Distribue une image avec interpolation bicubique pour un rendu seamless
     */
    public static byte[] distributeImageSeamless(byte[] originalImageData, RelativePosition relPos, 
                                               int totalWidth, int totalHeight, int totalDepth) {
        if (originalImageData == null || originalImageData.length == 0) {
            return null;
        }
        
        try {
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(originalImageData));
            if (originalImage == null) {
                return originalImageData;
            }
            
            // Calculer les dimensions de sortie optimales
            int outputWidth = 256;
            int outputHeight = 256;
            
            // Déterminer la stratégie selon la structure
            BufferedImage blockImage;
            if (totalDepth > 1) {
                // Structure 3D - distribution verticale et horizontale
                blockImage = distributeImage3DSeamless(originalImage, relPos, totalWidth, totalHeight, totalDepth, outputWidth, outputHeight);
            } else {
                // Structure 2D - distribution en grille
                blockImage = distributeImage2DSeamless(originalImage, relPos, totalWidth, totalHeight, outputWidth, outputHeight);
            }
            
            // Appliquer un lissage anti-aliasing
            blockImage = applyAntiAliasing(blockImage);
            
            // Convertir en byte array
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(blockImage, "PNG", baos);
            return baos.toByteArray();
            
        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur lors de la distribution d'image seamless", e);
            return originalImageData;
        }
    }
    
    /**
     * Distribution 2D avec interpolation bicubique
     */
    private static BufferedImage distributeImage2DSeamless(BufferedImage original, RelativePosition relPos,
                                                         int totalWidth, int totalHeight, 
                                                         int outputWidth, int outputHeight) {
        // Calculer les coordonnées exactes avec overlap pour seamless
        double segmentWidth = (double) original.getWidth() / totalWidth;
        double segmentHeight = (double) original.getHeight() / totalHeight;
        
        // Ajouter un petit overlap pour éviter les lignes de séparation
        double overlap = 0.5; // 0.5 pixel d'overlap
        
        double startX = relPos.x * segmentWidth - overlap;
        double startY = relPos.z * segmentHeight - overlap;
        double endX = (relPos.x + 1) * segmentWidth + overlap;
        double endY = (relPos.z + 1) * segmentHeight + overlap;
        
        // S'assurer que les coordonnées restent dans les limites
        startX = Math.max(0, startX);
        startY = Math.max(0, startY);
        endX = Math.min(original.getWidth(), endX);
        endY = Math.min(original.getHeight(), endY);
        
        // Extraire la région avec interpolation
        int regionWidth = (int) Math.ceil(endX - startX);
        int regionHeight = (int) Math.ceil(endY - startY);
        
        BufferedImage region = new BufferedImage(regionWidth, regionHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = region.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(original, 0, 0, regionWidth, regionHeight, 
                     (int) startX, (int) startY, (int) endX, (int) endY, null);
        g2d.dispose();
        
        // Redimensionner à la taille de sortie avec interpolation de haute qualité
        return resizeImageHighQuality(region, outputWidth, outputHeight);
    }
    
    /**
     * Distribution 3D avec support vertical
     */
    private static BufferedImage distributeImage3DSeamless(BufferedImage original, RelativePosition relPos,
                                                         int totalWidth, int totalHeight, int totalDepth,
                                                         int outputWidth, int outputHeight) {
        // Pour les structures 3D, utiliser X et Y pour la distribution
        double segmentWidth = (double) original.getWidth() / totalWidth;
        double segmentHeight = (double) original.getHeight() / totalDepth;
        
        double overlap = 0.5;
        
        double startX = relPos.x * segmentWidth - overlap;
        double startY = relPos.y * segmentHeight - overlap;
        double endX = (relPos.x + 1) * segmentWidth + overlap;
        double endY = (relPos.y + 1) * segmentHeight + overlap;
        
        startX = Math.max(0, startX);
        startY = Math.max(0, startY);
        endX = Math.min(original.getWidth(), endX);
        endY = Math.min(original.getHeight(), endY);
        
        int regionWidth = (int) Math.ceil(endX - startX);
        int regionHeight = (int) Math.ceil(endY - startY);
        
        BufferedImage region = new BufferedImage(regionWidth, regionHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = region.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(original, 0, 0, regionWidth, regionHeight, 
                     (int) startX, (int) startY, (int) endX, (int) endY, null);
        g2d.dispose();
        
        return resizeImageHighQuality(region, outputWidth, outputHeight);
    }
    
    /**
     * Redimensionnement d'image haute qualité avec interpolation bicubique
     */
    private static BufferedImage resizeImageHighQuality(BufferedImage original, int width, int height) {
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resized.createGraphics();
        
        // Configuration pour la meilleure qualité
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
        
        g2d.drawImage(original, 0, 0, width, height, null);
        g2d.dispose();
        
        return resized;
    }
    
    /**
     * Applique un lissage anti-aliasing pour réduire les artefacts
     */
    private static BufferedImage applyAntiAliasing(BufferedImage image) {
        BufferedImage smoothed = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = smoothed.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        return smoothed;
    }
    
    /**
     * Distribution de texte améliorée avec gestion des débordements
     */
    public static String distributeTextSeamless(String originalText, RelativePosition relPos,
                                              int totalWidth, int totalHeight, int totalDepth) {
        if (originalText == null || originalText.isEmpty()) {
            return "";
        }
        
        // Pour les petites structures, afficher le texte complet
        if (totalWidth <= 2 && totalHeight <= 2 && totalDepth <= 2) {
            return originalText;
        }
        
        String[] lines = originalText.split("\n");
        StringBuilder distributedText = new StringBuilder();
        
        // Calculer la distribution optimale
        int linesPerBlock;
        int startLine, endLine;
        
        if (totalDepth > 1) {
            // Structure 3D - distribution verticale
            linesPerBlock = Math.max(1, lines.length / totalDepth);
            startLine = relPos.y * linesPerBlock;
            endLine = Math.min(startLine + linesPerBlock, lines.length);
        } else {
            // Structure 2D - distribution horizontale
            linesPerBlock = Math.max(1, lines.length / totalHeight);
            startLine = relPos.z * linesPerBlock;
            endLine = Math.min(startLine + linesPerBlock, lines.length);
        }
        
        // Extraire et distribuer les lignes
        for (int i = startLine; i < endLine; i++) {
            if (i < lines.length) {
                String line = lines[i];
                
                // Distribution horizontale des caractères pour les grandes structures
                if (totalWidth > 2 && line.length() > totalWidth * 10) {
                    int charsPerBlock = Math.max(10, line.length() / totalWidth);
                    int startChar = relPos.x * charsPerBlock;
                    int endChar = Math.min(startChar + charsPerBlock, line.length());
                    
                    if (startChar < line.length()) {
                        line = line.substring(startChar, endChar);
                        
                        // Ajouter des indicateurs de continuation si nécessaire
                        if (startChar > 0) line = "..." + line;
                        if (endChar < lines[i].length()) line = line + "...";
                    }
                }
                
                distributedText.append(line);
                if (i < endLine - 1) {
                    distributedText.append("\n");
                }
            }
        }
        
        // Si aucun texte, afficher un indicateur de position
        if (distributedText.length() == 0) {
            return "§7[" + relPos.x + "," + relPos.y + "," + relPos.z + "]";
        }
        
        return distributedText.toString();
    }
}
