package com.pokecobble.screen;

import com.pokecobble.data.ScreenContent;
import com.pokecobble.ScreenBlock;
import com.pokecobble.screen.ConnectedScreenSystem.RelativePosition;
import com.pokecobble.util.ScreenLogger;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * Distributeur de contenu amélioré avec interpolation avancée et lissage seamless
 * Améliore la qualité visuelle pour les grandes structures d'écrans connectés
 */
public class EnhancedContentDistributor {
    
    /**
     * Distribue une image avec interpolation bicubique pour un rendu seamless
     */
    public static byte[] distributeImageSeamless(byte[] originalImageData, RelativePosition relPos,
                                               int totalWidth, int totalHeight, int totalDepth) {
        long startTime = ScreenLogger.startTimer();
        long startMemory = ScreenLogger.getCurrentMemoryUsage();

        ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
            "Début distribution seamless - Position: %s, Structure: %dx%dx%d, Taille image: %d bytes",
            relPos, totalWidth, totalHeight, totalDepth, originalImageData.length);

        if (originalImageData == null || originalImageData.length == 0) {
            ScreenLogger.warn(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Données d'image nulles ou vides pour la position %s", relPos);
            return null;
        }

        try {
            // Lire l'image originale
            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION, "Lecture de l'image originale...");
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(originalImageData));

            if (originalImage == null) {
                ScreenLogger.error(ScreenLogger.CATEGORY_DISTRIBUTION,
                    "Impossible de lire l'image pour la position %s", relPos);
                return originalImageData;
            }

            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Image lue avec succès - Dimensions: %dx%d, Type: %d",
                originalImage.getWidth(), originalImage.getHeight(), originalImage.getType());
            
            // Calculer les dimensions de sortie optimales
            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION, "Calcul des dimensions de sortie...");
            int outputWidth = 256;
            int outputHeight = 256;

            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Dimensions de sortie calculées: %dx%d", outputWidth, outputHeight);
            
            // Déterminer la stratégie selon la structure
            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Sélection de la stratégie de distribution - Structure 3D: %s", totalDepth > 1);

            BufferedImage blockImage;
            long distributionStart = ScreenLogger.startTimer();

            if (totalDepth > 1) {
                // Structure 3D - distribution verticale et horizontale
                ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                    "Application de la distribution 3D seamless...");
                blockImage = distributeImage3DSeamless(originalImage, relPos, totalWidth, totalHeight, totalDepth, outputWidth, outputHeight);
            } else {
                // Structure 2D - distribution en grille
                ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                    "Application de la distribution 2D seamless...");
                blockImage = distributeImage2DSeamless(originalImage, relPos, totalWidth, totalHeight, outputWidth, outputHeight);
            }

            long distributionTime = ScreenLogger.elapsedTime(distributionStart);
            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Distribution terminée en %dms", distributionTime);
            
            // Appliquer un lissage anti-aliasing
            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION, "Application de l'anti-aliasing...");
            long antiAliasingStart = ScreenLogger.startTimer();
            blockImage = applyAntiAliasing(blockImage);
            long antiAliasingTime = ScreenLogger.elapsedTime(antiAliasingStart);

            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Anti-aliasing terminé en %dms", antiAliasingTime);

            // Convertir en byte array
            ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION, "Conversion en byte array...");
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(blockImage, "PNG", baos);
            byte[] result = baos.toByteArray();

            // Logs de performance finaux
            long totalTime = ScreenLogger.elapsedTime(startTime);
            long endMemory = ScreenLogger.getCurrentMemoryUsage();
            long memoryUsed = endMemory - startMemory;

            ScreenLogger.performance("Distribution seamless", totalTime,
                String.format("Position: %s, Taille entrée: %d bytes, Taille sortie: %d bytes",
                    relPos, originalImageData.length, result.length));

            ScreenLogger.performanceWithMemory("Distribution seamless", totalTime, memoryUsed);

            ScreenLogger.info(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Distribution seamless réussie pour position %s - %d bytes générés",
                relPos, result.length);

            return result;
            
        } catch (Exception e) {
            long totalTime = ScreenLogger.elapsedTime(startTime);
            ScreenLogger.error(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Erreur lors de la distribution seamless pour position %s après %dms: %s",
                e, relPos, totalTime, e.getMessage());

            ScreenLogger.warn(ScreenLogger.CATEGORY_DISTRIBUTION,
                "Retour aux données d'image originales pour position %s", relPos);

            return originalImageData;
        }
    }
    
    /**
     * Distribution 2D avec interpolation bicubique
     */
    private static BufferedImage distributeImage2DSeamless(BufferedImage original, RelativePosition relPos,
                                                         int totalWidth, int totalHeight,
                                                         int outputWidth, int outputHeight) {
        ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
            "Distribution 2D - Position: (%d,%d), Grille: %dx%d",
            relPos.x, relPos.y, totalWidth, totalHeight);
        // Calculer les coordonnées exactes avec overlap pour seamless
        double segmentWidth = (double) original.getWidth() / totalWidth;
        double segmentHeight = (double) original.getHeight() / totalHeight;
        
        // Ajouter un petit overlap pour éviter les lignes de séparation
        double overlap = 0.5; // 0.5 pixel d'overlap
        
        double startX = relPos.x * segmentWidth - overlap;
        double startY = relPos.z * segmentHeight - overlap;
        double endX = (relPos.x + 1) * segmentWidth + overlap;
        double endY = (relPos.z + 1) * segmentHeight + overlap;
        
        // S'assurer que les coordonnées restent dans les limites
        startX = Math.max(0, startX);
        startY = Math.max(0, startY);
        endX = Math.min(original.getWidth(), endX);
        endY = Math.min(original.getHeight(), endY);
        
        // Extraire la région avec interpolation
        int regionWidth = (int) Math.ceil(endX - startX);
        int regionHeight = (int) Math.ceil(endY - startY);
        
        BufferedImage region = new BufferedImage(regionWidth, regionHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = region.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(original, 0, 0, regionWidth, regionHeight, 
                     (int) startX, (int) startY, (int) endX, (int) endY, null);
        g2d.dispose();
        
        // Redimensionner à la taille de sortie avec interpolation de haute qualité
        return resizeImageHighQuality(region, outputWidth, outputHeight);
    }
    
    /**
     * Distribution 3D avec support vertical
     */
    private static BufferedImage distributeImage3DSeamless(BufferedImage original, RelativePosition relPos,
                                                         int totalWidth, int totalHeight, int totalDepth,
                                                         int outputWidth, int outputHeight) {
        ScreenLogger.debug(ScreenLogger.CATEGORY_DISTRIBUTION,
            "Distribution 3D - Position: (%d,%d,%d), Structure: %dx%dx%d",
            relPos.x, relPos.y, relPos.z, totalWidth, totalHeight, totalDepth);
        // Pour les structures 3D, utiliser X et Y pour la distribution
        double segmentWidth = (double) original.getWidth() / totalWidth;
        double segmentHeight = (double) original.getHeight() / totalDepth;
        
        double overlap = 0.5;
        
        double startX = relPos.x * segmentWidth - overlap;
        double startY = relPos.y * segmentHeight - overlap;
        double endX = (relPos.x + 1) * segmentWidth + overlap;
        double endY = (relPos.y + 1) * segmentHeight + overlap;
        
        startX = Math.max(0, startX);
        startY = Math.max(0, startY);
        endX = Math.min(original.getWidth(), endX);
        endY = Math.min(original.getHeight(), endY);
        
        int regionWidth = (int) Math.ceil(endX - startX);
        int regionHeight = (int) Math.ceil(endY - startY);
        
        BufferedImage region = new BufferedImage(regionWidth, regionHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = region.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(original, 0, 0, regionWidth, regionHeight, 
                     (int) startX, (int) startY, (int) endX, (int) endY, null);
        g2d.dispose();
        
        return resizeImageHighQuality(region, outputWidth, outputHeight);
    }
    
    /**
     * Redimensionnement d'image haute qualité avec interpolation bicubique
     */
    private static BufferedImage resizeImageHighQuality(BufferedImage original, int width, int height) {
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resized.createGraphics();
        
        // Configuration pour la meilleure qualité
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
        
        g2d.drawImage(original, 0, 0, width, height, null);
        g2d.dispose();
        
        return resized;
    }
    
    /**
     * Applique un lissage anti-aliasing pour réduire les artefacts
     */
    private static BufferedImage applyAntiAliasing(BufferedImage image) {
        BufferedImage smoothed = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = smoothed.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        return smoothed;
    }
    
    /**
     * Distribution de texte améliorée avec gestion des débordements
     */
    public static String distributeTextSeamless(String originalText, RelativePosition relPos,
                                              int totalWidth, int totalHeight, int totalDepth) {
        if (originalText == null || originalText.isEmpty()) {
            return "";
        }
        
        // Pour les petites structures, afficher le texte complet
        if (totalWidth <= 2 && totalHeight <= 2 && totalDepth <= 2) {
            return originalText;
        }
        
        String[] lines = originalText.split("\n");
        StringBuilder distributedText = new StringBuilder();
        
        // Calculer la distribution optimale
        int linesPerBlock;
        int startLine, endLine;
        
        if (totalDepth > 1) {
            // Structure 3D - distribution verticale
            linesPerBlock = Math.max(1, lines.length / totalDepth);
            startLine = relPos.y * linesPerBlock;
            endLine = Math.min(startLine + linesPerBlock, lines.length);
        } else {
            // Structure 2D - distribution horizontale
            linesPerBlock = Math.max(1, lines.length / totalHeight);
            startLine = relPos.z * linesPerBlock;
            endLine = Math.min(startLine + linesPerBlock, lines.length);
        }
        
        // Extraire et distribuer les lignes
        for (int i = startLine; i < endLine; i++) {
            if (i < lines.length) {
                String line = lines[i];
                
                // Distribution horizontale des caractères pour les grandes structures
                if (totalWidth > 2 && line.length() > totalWidth * 10) {
                    int charsPerBlock = Math.max(10, line.length() / totalWidth);
                    int startChar = relPos.x * charsPerBlock;
                    int endChar = Math.min(startChar + charsPerBlock, line.length());
                    
                    if (startChar < line.length()) {
                        line = line.substring(startChar, endChar);
                        
                        // Ajouter des indicateurs de continuation si nécessaire
                        if (startChar > 0) line = "..." + line;
                        if (endChar < lines[i].length()) line = line + "...";
                    }
                }
                
                distributedText.append(line);
                if (i < endLine - 1) {
                    distributedText.append("\n");
                }
            }
        }
        
        // Si aucun texte, afficher un indicateur de position
        if (distributedText.length() == 0) {
            return "§7[" + relPos.x + "," + relPos.y + "," + relPos.z + "]";
        }
        
        return distributedText.toString();
    }
}
