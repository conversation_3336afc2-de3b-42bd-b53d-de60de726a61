package com.pokecobble.screen;

import com.pokecobble.ScreenBlock;
import com.pokecobble.util.ScreenLogger;
import com.pokecobble.registry.ModBlocks;
import net.minecraft.block.BlockState;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.world.World;

import java.util.*;

/**
 * Détecteur intelligent de connexions pour les écrans
 * Gère les connexions dans toutes les directions (horizontal + vertical)
 */
public class ScreenConnectionDetector {
    
    /**
     * Détecte toutes les connexions directes d'un bloc d'écran
     */
    public static Set<Direction> detectConnections(World world, BlockPos pos) {
        Set<Direction> connections = new HashSet<>();
        
        if (!isScreenBlock(world, pos)) {
            return connections;
        }
        
        // Vérifier chaque direction
        for (Direction direction : Direction.values()) {
            BlockPos adjacentPos = pos.offset(direction);
            if (isScreenBlock(world, adjacentPos)) {
                connections.add(direction);
            }
        }
        
        return connections;
    }
    
    /**
     * Trouve tous les blocs d'écran connectés à partir d'une position
     * Utilise un algorithme de recherche en largeur (BFS) pour une détection complète
     */
    public static Set<BlockPos> findConnectedBlocks(World world, BlockPos startPos) {
        long startTime = ScreenLogger.startTimer();
        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Recherche de blocs connectés depuis %s", startPos);

        if (!isScreenBlock(world, startPos)) {
            ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
                "Position de départ %s n'est pas un bloc d'écran", startPos);
            return new HashSet<>();
        }

        Set<BlockPos> visited = new HashSet<>();
        Set<BlockPos> connectedBlocks = new HashSet<>();
        Queue<BlockPos> toVisit = new LinkedList<>();

        toVisit.add(startPos);

        ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
            "Début de la recherche BFS depuis %s", startPos);
        
        while (!toVisit.isEmpty()) {
            BlockPos current = toVisit.poll();
            
            if (visited.contains(current)) {
                continue;
            }
            
            if (!isScreenBlock(world, current)) {
                continue;
            }
            
            visited.add(current);
            connectedBlocks.add(current);
            
            // Ajouter tous les voisins directs (6 directions)
            for (Direction direction : Direction.values()) {
                BlockPos neighbor = current.offset(direction);
                if (!visited.contains(neighbor) && isScreenBlock(world, neighbor)) {
                    toVisit.add(neighbor);
                }
            }
        }

        long elapsedTime = ScreenLogger.elapsedTime(startTime);
        ScreenLogger.info(ScreenLogger.CATEGORY_CONNECTION,
            "Recherche BFS terminée en %dms - %d blocs connectés trouvés depuis %s",
            elapsedTime, connectedBlocks.size(), startPos);

        if (connectedBlocks.size() > 1) {
            ScreenLogger.debug(ScreenLogger.CATEGORY_CONNECTION,
                "Blocs connectés: %s", connectedBlocks);
        }

        return connectedBlocks;
    }
    
    /**
     * Trouve tous les blocs connectés avec support des connexions étendues (diagonales)
     * Pour des structures plus complexes
     */
    public static Set<BlockPos> findConnectedBlocksExtended(World world, BlockPos startPos) {
        if (!isScreenBlock(world, startPos)) {
            return new HashSet<>();
        }
        
        Set<BlockPos> visited = new HashSet<>();
        Set<BlockPos> connectedBlocks = new HashSet<>();
        Queue<BlockPos> toVisit = new LinkedList<>();
        
        toVisit.add(startPos);
        
        while (!toVisit.isEmpty()) {
            BlockPos current = toVisit.poll();
            
            if (visited.contains(current)) {
                continue;
            }
            
            if (!isScreenBlock(world, current)) {
                continue;
            }
            
            visited.add(current);
            connectedBlocks.add(current);
            
            // Ajouter tous les voisins étendus (26 directions)
            for (BlockPos neighbor : getExtendedNeighbors(current)) {
                if (!visited.contains(neighbor) && isScreenBlock(world, neighbor)) {
                    toVisit.add(neighbor);
                }
            }
        }
        
        ScreenBlock.LOGGER.info("Détection connexions étendues: " + connectedBlocks.size() + " blocs connectés depuis " + startPos);
        return connectedBlocks;
    }
    
    /**
     * Analyse la forme d'une structure connectée
     */
    public static StructureAnalysis analyzeStructure(Set<BlockPos> blocks) {
        if (blocks.isEmpty()) {
            return new StructureAnalysis(StructureType.NONE, 0, 0, 0);
        }
        
        // Calculer les dimensions
        int minX = blocks.stream().mapToInt(pos -> pos.getX()).min().orElse(0);
        int maxX = blocks.stream().mapToInt(pos -> pos.getX()).max().orElse(0);
        int minY = blocks.stream().mapToInt(pos -> pos.getY()).min().orElse(0);
        int maxY = blocks.stream().mapToInt(pos -> pos.getY()).max().orElse(0);
        int minZ = blocks.stream().mapToInt(pos -> pos.getZ()).min().orElse(0);
        int maxZ = blocks.stream().mapToInt(pos -> pos.getZ()).max().orElse(0);
        
        int width = maxX - minX + 1;
        int height = maxZ - minZ + 1;
        int depth = maxY - minY + 1;
        
        // Déterminer le type de structure
        StructureType type = determineStructureType(width, height, depth, blocks.size());
        
        return new StructureAnalysis(type, width, height, depth);
    }
    
    /**
     * Détermine le type de structure basé sur les dimensions
     */
    private static StructureType determineStructureType(int width, int height, int depth, int blockCount) {
        // Structure unique
        if (blockCount == 1) {
            return StructureType.SINGLE;
        }
        
        // Structure verticale pure (tour)
        if (width == 1 && height == 1 && depth > 1) {
            return StructureType.VERTICAL_TOWER;
        }
        
        // Structure horizontale pure (ligne)
        if (depth == 1 && (width > 1 || height > 1)) {
            if (width == 1 || height == 1) {
                return StructureType.HORIZONTAL_LINE;
            } else {
                return StructureType.HORIZONTAL_RECTANGLE;
            }
        }
        
        // Mur vertical
        if (depth > 1 && (width > 1 || height > 1)) {
            return StructureType.VERTICAL_WALL;
        }
        
        // Structure 3D complexe
        if (width > 1 && height > 1 && depth > 1) {
            return StructureType.COMPLEX_3D;
        }
        
        // Structure irrégulière
        return StructureType.IRREGULAR;
    }
    
    /**
     * Vérifie si un bloc est un bloc d'écran
     */
    public static boolean isScreenBlock(World world, BlockPos pos) {
        BlockState state = world.getBlockState(pos);
        return state.isOf(ModBlocks.SCREEN_DISPLAY_BLOCK);
    }
    
    /**
     * Obtient tous les voisins directs (6 directions)
     */
    public static List<BlockPos> getDirectNeighbors(BlockPos pos) {
        return Arrays.asList(
            pos.north(),
            pos.south(),
            pos.east(),
            pos.west(),
            pos.up(),
            pos.down()
        );
    }
    
    /**
     * Obtient tous les voisins étendus (26 directions)
     */
    public static List<BlockPos> getExtendedNeighbors(BlockPos pos) {
        List<BlockPos> neighbors = new ArrayList<>();
        
        // 6 faces principales
        neighbors.addAll(getDirectNeighbors(pos));
        
        // 12 arêtes
        neighbors.add(pos.north().east());
        neighbors.add(pos.north().west());
        neighbors.add(pos.south().east());
        neighbors.add(pos.south().west());
        neighbors.add(pos.north().up());
        neighbors.add(pos.north().down());
        neighbors.add(pos.south().up());
        neighbors.add(pos.south().down());
        neighbors.add(pos.east().up());
        neighbors.add(pos.east().down());
        neighbors.add(pos.west().up());
        neighbors.add(pos.west().down());
        
        // 8 coins
        neighbors.add(pos.north().east().up());
        neighbors.add(pos.north().east().down());
        neighbors.add(pos.north().west().up());
        neighbors.add(pos.north().west().down());
        neighbors.add(pos.south().east().up());
        neighbors.add(pos.south().east().down());
        neighbors.add(pos.south().west().up());
        neighbors.add(pos.south().west().down());
        
        return neighbors;
    }
    
    /**
     * Types de structures détectées
     */
    public enum StructureType {
        NONE,                    // Aucune structure
        SINGLE,                  // Bloc unique
        HORIZONTAL_LINE,         // Ligne horizontale
        HORIZONTAL_RECTANGLE,    // Rectangle horizontal
        VERTICAL_TOWER,          // Tour verticale
        VERTICAL_WALL,           // Mur vertical
        COMPLEX_3D,              // Structure 3D complexe
        IRREGULAR                // Structure irrégulière
    }
    
    /**
     * Résultat de l'analyse d'une structure
     */
    public static class StructureAnalysis {
        public final StructureType type;
        public final int width, height, depth;
        
        public StructureAnalysis(StructureType type, int width, int height, int depth) {
            this.type = type;
            this.width = width;
            this.height = height;
            this.depth = depth;
        }
        
        @Override
        public String toString() {
            return type + " (" + width + "x" + height + "x" + depth + ")";
        }
        
        /**
         * Vérifie si la structure est verticale
         */
        public boolean isVertical() {
            return type == StructureType.VERTICAL_TOWER || type == StructureType.VERTICAL_WALL;
        }
        
        /**
         * Vérifie si la structure est horizontale
         */
        public boolean isHorizontal() {
            return type == StructureType.HORIZONTAL_LINE || type == StructureType.HORIZONTAL_RECTANGLE;
        }
        
        /**
         * Vérifie si la structure est 3D
         */
        public boolean is3D() {
            return type == StructureType.COMPLEX_3D || depth > 1;
        }
    }
}
