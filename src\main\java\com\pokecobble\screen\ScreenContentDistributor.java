package com.pokecobble.screen;

import com.pokecobble.ScreenBlock;
import com.pokecobble.data.ScreenContent;
import com.pokecobble.screen.ConnectedScreenSystem.RelativePosition;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * Distributeur intelligent de contenu pour les écrans connectés
 * Gère la distribution seamless d'images et de texte sur les structures 3D
 */
public class ScreenContentDistributor {
    
    /**
     * Distribue le contenu sur un bloc spécifique d'une structure
     */
    public static ScreenContent distributeContent(ScreenContent sourceContent, RelativePosition relPos, 
                                                 int totalWidth, int totalHeight, int totalDepth) {
        if (sourceContent == null) {
            return new ScreenContent();
        }
        
        // Créer une copie du contenu source
        ScreenContent distributedContent = sourceContent.copy();
        
        // Configurer les propriétés multibloc
        distributedContent.setMultiblockPosition(relPos.x, relPos.y, relPos.z);
        distributedContent.setMultiblockSize(totalWidth, totalHeight, totalDepth);
        distributedContent.setUnifiedScreen(true);
        
        // Distribuer l'image si présente
        if (sourceContent.hasImageData()) {
            byte[] distributedImage = distributeImage(sourceContent.getImageData(), 
                                                    relPos, totalWidth, totalHeight, totalDepth);
            if (distributedImage != null) {
                distributedContent.setImageData(distributedImage);
            }
        }
        
        // Distribuer le texte si présent
        if (!sourceContent.getDisplayText().isEmpty()) {
            String distributedText = distributeText(sourceContent.getDisplayText(), 
                                                  relPos, totalWidth, totalHeight, totalDepth);
            distributedContent.setDisplayText(distributedText);
        }
        
        return distributedContent;
    }
    
    /**
     * Distribue une image sur un bloc spécifique
     */
    public static byte[] distributeImage(byte[] originalImageData, RelativePosition relPos,
                                       int totalWidth, int totalHeight, int totalDepth) {
        if (originalImageData == null || originalImageData.length == 0) {
            return null;
        }

        // Utiliser le distributeur amélioré pour une meilleure qualité
        if (totalWidth > 2 || totalHeight > 2 || totalDepth > 2) {
            byte[] enhancedResult = EnhancedContentDistributor.distributeImageSeamless(
                originalImageData, relPos, totalWidth, totalHeight, totalDepth);
            if (enhancedResult != null) {
                return enhancedResult;
            }
        }

        try {
            // Fallback vers la méthode standard
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(originalImageData));
            if (originalImage == null) {
                return originalImageData;
            }

            // Déterminer la stratégie de distribution selon la structure
            DistributionStrategy strategy = determineDistributionStrategy(totalWidth, totalHeight, totalDepth);

            // Appliquer la stratégie
            BufferedImage blockImage = switch (strategy) {
                case HORIZONTAL_GRID -> distributeImageHorizontalGrid(originalImage, relPos, totalWidth, totalHeight);
                case VERTICAL_STACK -> distributeImageVerticalStack(originalImage, relPos, totalDepth);
                case MIXED_3D -> distributeImageMixed3D(originalImage, relPos, totalWidth, totalHeight, totalDepth);
                case SINGLE_BLOCK -> originalImage;
            };

            // Redimensionner à la taille standard si nécessaire
            if (blockImage.getWidth() != 256 || blockImage.getHeight() != 256) {
                blockImage = resizeImage(blockImage, 256, 256);
            }

            // Convertir en byte array
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(blockImage, "PNG", baos);
            return baos.toByteArray();

        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur lors de la distribution d'image", e);
            return originalImageData;
        }
    }
    
    /**
     * Distribue le texte sur un bloc spécifique
     */
    public static String distributeText(String originalText, RelativePosition relPos,
                                      int totalWidth, int totalHeight, int totalDepth) {
        if (originalText == null || originalText.isEmpty()) {
            return "";
        }

        // Utiliser le distributeur amélioré pour les grandes structures
        if (totalWidth > 2 || totalHeight > 2 || totalDepth > 2) {
            return EnhancedContentDistributor.distributeTextSeamless(
                originalText, relPos, totalWidth, totalHeight, totalDepth);
        }

        // Pour les petites structures, afficher le texte complet
        if (totalWidth <= 2 && totalHeight <= 2 && totalDepth <= 2) {
            return originalText;
        }

        // Déterminer la stratégie de distribution
        DistributionStrategy strategy = determineDistributionStrategy(totalWidth, totalHeight, totalDepth);

        return switch (strategy) {
            case HORIZONTAL_GRID -> distributeTextHorizontalGrid(originalText, relPos, totalWidth, totalHeight);
            case VERTICAL_STACK -> distributeTextVerticalStack(originalText, relPos, totalDepth);
            case MIXED_3D -> distributeTextMixed3D(originalText, relPos, totalWidth, totalHeight, totalDepth);
            case SINGLE_BLOCK -> originalText;
        };
    }
    
    /**
     * Détermine la stratégie de distribution optimale
     */
    private static DistributionStrategy determineDistributionStrategy(int width, int height, int depth) {
        // Bloc unique
        if (width == 1 && height == 1 && depth == 1) {
            return DistributionStrategy.SINGLE_BLOCK;
        }
        
        // Structure verticale pure (tour)
        if (width == 1 && height == 1 && depth > 1) {
            return DistributionStrategy.VERTICAL_STACK;
        }
        
        // Structure horizontale pure
        if (depth == 1) {
            return DistributionStrategy.HORIZONTAL_GRID;
        }
        
        // Structure 3D mixte
        return DistributionStrategy.MIXED_3D;
    }
    
    // Méthodes de distribution d'images
    
    private static BufferedImage distributeImageHorizontalGrid(BufferedImage original, RelativePosition relPos, 
                                                             int totalWidth, int totalHeight) {
        int segmentWidth = original.getWidth() / totalWidth;
        int segmentHeight = original.getHeight() / totalHeight;
        
        int startX = relPos.x * segmentWidth;
        int startY = relPos.z * segmentHeight;
        
        int endX = Math.min(startX + segmentWidth, original.getWidth());
        int endY = Math.min(startY + segmentHeight, original.getHeight());
        
        return original.getSubimage(startX, startY, endX - startX, endY - startY);
    }
    
    private static BufferedImage distributeImageVerticalStack(BufferedImage original, RelativePosition relPos, 
                                                            int totalDepth) {
        int segmentHeight = original.getHeight() / totalDepth;
        
        int startY = relPos.y * segmentHeight;
        int endY = Math.min(startY + segmentHeight, original.getHeight());
        
        return original.getSubimage(0, startY, original.getWidth(), endY - startY);
    }
    
    private static BufferedImage distributeImageMixed3D(BufferedImage original, RelativePosition relPos, 
                                                       int totalWidth, int totalHeight, int totalDepth) {
        // Pour les structures 3D, utiliser X et Y pour la distribution
        int segmentWidth = original.getWidth() / totalWidth;
        int segmentHeight = original.getHeight() / totalDepth;
        
        int startX = relPos.x * segmentWidth;
        int startY = relPos.y * segmentHeight;
        
        int endX = Math.min(startX + segmentWidth, original.getWidth());
        int endY = Math.min(startY + segmentHeight, original.getHeight());
        
        return original.getSubimage(startX, startY, endX - startX, endY - startY);
    }
    
    // Méthodes de distribution de texte
    
    private static String distributeTextHorizontalGrid(String originalText, RelativePosition relPos, 
                                                     int totalWidth, int totalHeight) {
        String[] lines = originalText.split("\n");
        StringBuilder result = new StringBuilder();
        
        int linesPerRow = Math.max(1, lines.length / totalHeight);
        int startLine = relPos.z * linesPerRow;
        int endLine = Math.min(startLine + linesPerRow, lines.length);
        
        for (int i = startLine; i < endLine; i++) {
            if (i < lines.length) {
                String line = lines[i];
                
                // Distribution horizontale des caractères
                if (totalWidth > 1 && line.length() > totalWidth) {
                    int charsPerBlock = line.length() / totalWidth;
                    int startChar = relPos.x * charsPerBlock;
                    int endChar = Math.min(startChar + charsPerBlock, line.length());
                    
                    if (startChar < line.length()) {
                        line = line.substring(startChar, endChar);
                    }
                }
                
                result.append(line);
                if (i < endLine - 1) {
                    result.append("\n");
                }
            }
        }
        
        return result.length() > 0 ? result.toString() : "§7[" + relPos.x + "," + relPos.z + "]";
    }
    
    private static String distributeTextVerticalStack(String originalText, RelativePosition relPos, 
                                                    int totalDepth) {
        String[] lines = originalText.split("\n");
        StringBuilder result = new StringBuilder();
        
        int linesPerLevel = Math.max(1, lines.length / totalDepth);
        int startLine = relPos.y * linesPerLevel;
        int endLine = Math.min(startLine + linesPerLevel, lines.length);
        
        for (int i = startLine; i < endLine; i++) {
            if (i < lines.length) {
                result.append(lines[i]);
                if (i < endLine - 1) {
                    result.append("\n");
                }
            }
        }
        
        return result.length() > 0 ? result.toString() : "§7[Y=" + relPos.y + "]";
    }
    
    private static String distributeTextMixed3D(String originalText, RelativePosition relPos, 
                                              int totalWidth, int totalHeight, int totalDepth) {
        // Pour les structures 3D, privilégier la distribution verticale
        return distributeTextVerticalStack(originalText, relPos, totalDepth);
    }
    
    // Méthodes utilitaires
    
    private static BufferedImage resizeImage(BufferedImage original, int width, int height) {
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resized.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, width, height, null);
        g2d.dispose();
        return resized;
    }
    
    /**
     * Stratégies de distribution de contenu
     */
    private enum DistributionStrategy {
        SINGLE_BLOCK,      // Bloc unique - pas de distribution
        HORIZONTAL_GRID,   // Grille horizontale - distribution X,Z
        VERTICAL_STACK,    // Pile verticale - distribution Y
        MIXED_3D          // Structure 3D - distribution X,Y
    }
}
