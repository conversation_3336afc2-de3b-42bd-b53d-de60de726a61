package com.pokecobble.test;

import com.pokecobble.screen.EnhancedContentDistributor;
import com.pokecobble.screen.ConnectedScreenSystem.RelativePosition;
import com.pokecobble.ScreenBlock;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;

/**
 * Tests automatisés pour la distribution seamless de contenu
 */
public class SeamlessDistributionTest {
    
    /**
     * Teste la distribution d'images sur différentes tailles de structures
     */
    public static void testImageDistribution() {
        ScreenBlock.LOGGER.info("=== Test de Distribution d'Images Seamless ===");
        
        try {
            // Créer une image de test avec un motif reconnaissable
            BufferedImage testImage = createTestImage(512, 512);
            byte[] imageData = imageToByteArray(testImage);
            
            // Test 1: Structure 2x2
            testStructure(imageData, 2, 2, 1, "2x2");
            
            // Test 2: Structure 3x3
            testStructure(imageData, 3, 3, 1, "3x3");
            
            // Test 3: Structure 4x4
            testStructure(imageData, 4, 4, 1, "4x4");
            
            // Test 4: Structure 3D (2x2x2)
            testStructure(imageData, 2, 2, 2, "2x2x2 (3D)");
            
            ScreenBlock.LOGGER.info("✅ Tous les tests de distribution d'images réussis !");
            
        } catch (Exception e) {
            ScreenBlock.LOGGER.error("❌ Erreur lors des tests de distribution d'images", e);
        }
    }
    
    /**
     * Teste la distribution de texte sur différentes structures
     */
    public static void testTextDistribution() {
        ScreenBlock.LOGGER.info("=== Test de Distribution de Texte Seamless ===");
        
        String testText = createTestText();
        
        // Test différentes structures
        testTextOnStructure(testText, 2, 2, 1, "2x2");
        testTextOnStructure(testText, 3, 3, 1, "3x3");
        testTextOnStructure(testText, 4, 4, 1, "4x4");
        testTextOnStructure(testText, 2, 2, 3, "2x2x3 (3D)");
        
        ScreenBlock.LOGGER.info("✅ Tous les tests de distribution de texte réussis !");
    }
    
    /**
     * Teste une structure spécifique avec une image
     */
    private static void testStructure(byte[] imageData, int width, int height, int depth, String structureName) {
        ScreenBlock.LOGGER.info("Test structure " + structureName + ":");
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < depth; y++) {
                for (int z = 0; z < height; z++) {
                    RelativePosition relPos = new RelativePosition(x, y, z);
                    
                    byte[] distributedImage = EnhancedContentDistributor.distributeImageSeamless(
                        imageData, relPos, width, height, depth);
                    
                    if (distributedImage != null && distributedImage.length > 0) {
                        ScreenBlock.LOGGER.info("  ✓ Bloc (" + x + "," + y + "," + z + ") - " + 
                                               distributedImage.length + " bytes");
                    } else {
                        ScreenBlock.LOGGER.warn("  ⚠ Bloc (" + x + "," + y + "," + z + ") - Échec distribution");
                    }
                }
            }
        }
    }
    
    /**
     * Teste la distribution de texte sur une structure
     */
    private static void testTextOnStructure(String text, int width, int height, int depth, String structureName) {
        ScreenBlock.LOGGER.info("Test texte structure " + structureName + ":");
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < depth; y++) {
                for (int z = 0; z < height; z++) {
                    RelativePosition relPos = new RelativePosition(x, y, z);
                    
                    String distributedText = EnhancedContentDistributor.distributeTextSeamless(
                        text, relPos, width, height, depth);
                    
                    ScreenBlock.LOGGER.info("  ✓ Bloc (" + x + "," + y + "," + z + ") - \"" + 
                                           (distributedText.length() > 30 ? 
                                            distributedText.substring(0, 30) + "..." : distributedText) + "\"");
                }
            }
        }
    }
    
    /**
     * Crée une image de test avec un motif en damier coloré
     */
    private static BufferedImage createTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // Fond dégradé
        GradientPaint gradient = new GradientPaint(0, 0, Color.BLUE, width, height, Color.RED);
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, width, height);
        
        // Motif en damier pour tester la continuité
        g2d.setColor(Color.WHITE);
        int squareSize = 32;
        for (int x = 0; x < width; x += squareSize * 2) {
            for (int y = 0; y < height; y += squareSize * 2) {
                g2d.fillRect(x, y, squareSize, squareSize);
                g2d.fillRect(x + squareSize, y + squareSize, squareSize, squareSize);
            }
        }
        
        // Lignes de grille pour vérifier l'alignement
        g2d.setColor(Color.YELLOW);
        g2d.setStroke(new BasicStroke(2));
        for (int x = 0; x < width; x += width / 4) {
            g2d.drawLine(x, 0, x, height);
        }
        for (int y = 0; y < height; y += height / 4) {
            g2d.drawLine(0, y, width, y);
        }
        
        // Texte de test
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.BOLD, 24));
        g2d.drawString("TEST SEAMLESS", width / 4, height / 2);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * Crée un texte de test multi-lignes
     */
    private static String createTestText() {
        return """
            Ligne 1: Test de distribution seamless
            Ligne 2: Écrans connectés automatiquement
            Ligne 3: Structures 2D et 3D supportées
            Ligne 4: Qualité d'image optimisée
            Ligne 5: Interpolation bicubique
            Ligne 6: Anti-aliasing avancé
            Ligne 7: Distribution intelligente
            Ligne 8: Performance optimisée
            Ligne 9: Interface moderne
            Ligne 10: Système complet et robuste
            Ligne 11: Tests automatisés
            Ligne 12: Fin du test de distribution
            """;
    }
    
    /**
     * Convertit une BufferedImage en byte array
     */
    private static byte[] imageToByteArray(BufferedImage image) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", baos);
            return baos.toByteArray();
        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur conversion image", e);
            return new byte[0];
        }
    }
    
    /**
     * Lance tous les tests
     */
    public static void runAllTests() {
        ScreenBlock.LOGGER.info("🚀 Démarrage des tests de distribution seamless...");
        
        testImageDistribution();
        testTextDistribution();
        
        ScreenBlock.LOGGER.info("🎉 Tests de distribution seamless terminés !");
    }
}
