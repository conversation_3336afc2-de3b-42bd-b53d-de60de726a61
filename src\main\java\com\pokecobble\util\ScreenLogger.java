package com.pokecobble.util;

import com.pokecobble.ScreenBlock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Système de logging configuré pour le mod Screen Block
 * Permet de contrôler la verbosité des logs selon les besoins
 */
public class ScreenLogger {
    
    public enum LogLevel {
        DEBUG(0),
        INFO(1),
        WARN(2),
        ERROR(3);
        
        private final int level;
        
        LogLevel(int level) {
            this.level = level;
        }
        
        public boolean shouldLog(LogLevel targetLevel) {
            return this.level <= targetLevel.level;
        }
    }
    
    private static LogLevel currentLogLevel = LogLevel.INFO;
    private static final Logger LOGGER = LoggerFactory.getLogger(ScreenBlock.MODID);
    
    // Catégories de logging
    public static final String CATEGORY_DISTRIBUTION = "[DISTRIBUTION]";
    public static final String CATEGORY_CONNECTION = "[CONNECTION]";
    public static final String CATEGORY_WEB_SERVER = "[WEB_SERVER]";
    public static final String CATEGORY_PERFORMANCE = "[PERFORMANCE]";
    public static final String CATEGORY_TEST = "[TEST]";
    public static final String CATEGORY_SYNC = "[SYNC]";
    
    /**
     * Configure le niveau de log global
     */
    public static void setLogLevel(LogLevel level) {
        currentLogLevel = level;
        info(CATEGORY_DISTRIBUTION, "Niveau de log configuré à: " + level.name());
    }
    
    /**
     * Obtient le niveau de log actuel
     */
    public static LogLevel getLogLevel() {
        return currentLogLevel;
    }
    
    /**
     * Log de débogage détaillé
     */
    public static void debug(String category, String message, Object... args) {
        if (currentLogLevel.shouldLog(LogLevel.DEBUG)) {
            LOGGER.info("{} [DEBUG] {}", category, formatMessage(message, args));
        }
    }
    
    /**
     * Log d'information générale
     */
    public static void info(String category, String message, Object... args) {
        if (currentLogLevel.shouldLog(LogLevel.INFO)) {
            LOGGER.info("{} {}", category, formatMessage(message, args));
        }
    }
    
    /**
     * Log d'avertissement
     */
    public static void warn(String category, String message, Object... args) {
        if (currentLogLevel.shouldLog(LogLevel.WARN)) {
            LOGGER.warn("{} {}", category, formatMessage(message, args));
        }
    }
    
    /**
     * Log d'erreur
     */
    public static void error(String category, String message, Object... args) {
        if (currentLogLevel.shouldLog(LogLevel.ERROR)) {
            LOGGER.error("{} {}", category, formatMessage(message, args));
        }
    }
    
    /**
     * Log d'erreur avec exception
     */
    public static void error(String category, String message, Throwable throwable, Object... args) {
        if (currentLogLevel.shouldLog(LogLevel.ERROR)) {
            LOGGER.error("{} {}", category, formatMessage(message, args), throwable);
        }
    }
    
    /**
     * Log de performance avec métriques
     */
    public static void performance(String operation, long timeMs, String details) {
        if (currentLogLevel.shouldLog(LogLevel.DEBUG)) {
            LOGGER.info("{} Opération '{}' terminée en {}ms - {}", 
                CATEGORY_PERFORMANCE, operation, timeMs, details);
        }
    }
    
    /**
     * Log de performance avec métriques mémoire
     */
    public static void performanceWithMemory(String operation, long timeMs, long memoryUsedBytes) {
        if (currentLogLevel.shouldLog(LogLevel.DEBUG)) {
            double memoryMB = memoryUsedBytes / (1024.0 * 1024.0);
            LOGGER.info("{} Opération '{}' terminée en {}ms, mémoire utilisée: {:.2f}MB", 
                CATEGORY_PERFORMANCE, operation, timeMs, memoryMB);
        }
    }
    
    /**
     * Formate un message avec des arguments
     */
    private static String formatMessage(String message, Object... args) {
        if (args.length == 0) {
            return message;
        }
        try {
            return String.format(message, args);
        } catch (Exception e) {
            return message + " [ERREUR_FORMAT: " + e.getMessage() + "]";
        }
    }
    
    /**
     * Crée un timestamp pour les logs de performance
     */
    public static long startTimer() {
        return System.currentTimeMillis();
    }
    
    /**
     * Calcule le temps écoulé depuis un timestamp
     */
    public static long elapsedTime(long startTime) {
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * Obtient l'utilisation mémoire actuelle
     */
    public static long getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * Log de démarrage d'opération avec timer
     */
    public static long logOperationStart(String category, String operation, String details) {
        debug(category, "Démarrage de l'opération '%s' - %s", operation, details);
        return startTimer();
    }
    
    /**
     * Log de fin d'opération avec métriques
     */
    public static void logOperationEnd(String category, String operation, long startTime, String result) {
        long elapsed = elapsedTime(startTime);
        debug(category, "Fin de l'opération '%s' en %dms - %s", operation, elapsed, result);
    }
}
