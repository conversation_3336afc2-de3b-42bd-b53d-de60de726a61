package com.pokecobble.web;

import com.pokecobble.ScreenBlock;
import com.pokecobble.block.entity.ScreenDisplayBlockEntity;
import com.pokecobble.data.ScreenContent;
import com.pokecobble.multiblock.AdvancedMultiblockManager;
import com.pokecobble.multiblock.AutoUnifiedScreenManager;
import com.pokecobble.multiblock.UnifiedScreenManager;
import com.pokecobble.web.DynamicWebInterface;
import com.pokecobble.display.DynamicScreenDisplaySystem;
import com.pokecobble.network.ScreenNetworkHandler;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.util.Base64;

import java.io.*;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;

/**
 * Serveur web intégré pour l'éditeur d'écran moderne
 */
public class ScreenWebServer {

    private static final int PORT = 8081;
    private static HttpServer server;
    private static boolean isRunning = false;
    private static MinecraftServer minecraftServer;

    // Stockage temporaire des modifications en cours
    private static final Map<String, ScreenEditSession> editSessions = new HashMap<>();

    public static void setMinecraftServer(MinecraftServer server) {
        minecraftServer = server;
    }

    private static World getServerWorld() {
        if (minecraftServer != null) {
            return minecraftServer.getOverworld();
        }
        return null;
    }

    public static void start() {
        if (isRunning) return;

        try {
            server = HttpServer.create(new InetSocketAddress(PORT), 0);

            // Routes de l'API
            server.createContext("/", new MainPageHandler());
            server.createContext("/api/screen", new ScreenApiHandler());
            server.createContext("/api/upload", new ImageUploadHandler());
            server.createContext("/api/save", new SaveHandler());
            server.createContext("/editor", new EditorPageHandler());

        // New dynamic endpoints
        server.createContext("/dynamic-editor", new DynamicEditorPageHandler());
        server.createContext("/api/session-info", new SessionInfoHandler());
        server.createContext("/api/check-structure-changes", new StructureChangeHandler());
        server.createContext("/api/update-content", new UpdateContentHandler());
            server.createContext("/api/enhanced", new EnhancedHandler());

            server.setExecutor(Executors.newFixedThreadPool(4));
            server.start();

            isRunning = true;
            ScreenBlock.LOGGER.info("Serveur web Screen Block démarré sur http://localhost:" + PORT);

        } catch (IOException e) {
            ScreenBlock.LOGGER.error("Erreur lors du démarrage du serveur web", e);
        }
    }

    public static void stop() {
        if (server != null && isRunning) {
            server.stop(0);
            isRunning = false;
            ScreenBlock.LOGGER.info("Serveur web Screen Block arrêté");
        }
    }

    public static boolean isRunning() {
        return isRunning;
    }

    public static void openEditor(BlockPos pos) {
        if (!isRunning) {
            start();
        }

        // Create enhanced dynamic session
        World world = getServerWorld();
        if (world != null) {
            String sessionId = DynamicWebInterface.createEnhancedSession(world, pos);
            if (sessionId != null) {
                // Also create legacy session for compatibility
                ScreenEditSession session = new ScreenEditSession(pos);
                editSessions.put(sessionId, session);

                // Open browser with dynamic editor
                String url = "http://localhost:" + PORT + "/dynamic-editor?session=" + sessionId;
                openBrowser(url);

                ScreenBlock.LOGGER.info("Opened dynamic editor for position: " + pos + " with session: " + sessionId);
            } else {
                // Fallback to legacy editor
                String legacySessionId = pos.getX() + "_" + pos.getY() + "_" + pos.getZ();
                ScreenEditSession session = new ScreenEditSession(pos);
                editSessions.put(legacySessionId, session);

                String url = "http://localhost:" + PORT + "/editor?session=" + legacySessionId;
                openBrowser(url);

                ScreenBlock.LOGGER.warn("Fallback to legacy editor for position: " + pos);
            }
        }
    }

    private static void openBrowser(String url) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
            } else if (os.contains("mac")) {
                Runtime.getRuntime().exec("open " + url);
            } else if (os.contains("nix") || os.contains("nux")) {
                Runtime.getRuntime().exec("xdg-open " + url);
            }
        } catch (IOException e) {
            ScreenBlock.LOGGER.error("Impossible d'ouvrir le navigateur", e);
        }
    }

    // Handler pour la page principale
    static class MainPageHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Screen Block Editor</title>
                    <meta charset="UTF-8">
                </head>
                <body>
                    <h1>Screen Block Editor</h1>
                    <p>Serveur web actif pour l'édition des écrans Minecraft</p>
                    <p>Utilisez clic droit sur un bloc d'écran dans le jeu pour ouvrir l'éditeur</p>
                </body>
                </html>
                """;

            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }
    }

    // Handler pour l'API des écrans
    static class ScreenApiHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String method = exchange.getRequestMethod();
            String path = exchange.getRequestURI().getPath();
            String query = exchange.getRequestURI().getQuery();

            // Ajouter les headers CORS
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");

            if ("OPTIONS".equals(method)) {
                exchange.sendResponseHeaders(200, 0);
                exchange.getResponseBody().close();
                return;
            }

            String response = "";

            if ("GET".equals(method)) {
                // Récupérer les données d'un écran
                response = handleGetScreen(query);
            } else if ("POST".equals(method)) {
                // Mettre à jour un écran
                response = handleUpdateScreen(exchange);
            }

            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.sendResponseHeaders(200, response.getBytes().length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }

        private String handleGetScreen(String query) {
            try {
                if (query != null && query.contains("session=")) {
                    String sessionId = query.split("session=")[1].split("&")[0];
                    ScreenEditSession session = editSessions.get(sessionId);

                    if (session != null) {
                        World world = getServerWorld();
                        if (world != null && world.getBlockEntity(session.getPosition()) instanceof ScreenDisplayBlockEntity entity) {
                            ScreenContent content = entity.getContent();

                            // Construire la réponse JSON avec toutes les données
                            StringBuilder json = new StringBuilder();
                            json.append("{");
                            json.append("\"status\":\"ok\",");
                            json.append("\"data\":{");
                            json.append("\"position\":{");
                            json.append("\"x\":").append(session.getPosition().getX()).append(",");
                            json.append("\"y\":").append(session.getPosition().getY()).append(",");
                            json.append("\"z\":").append(session.getPosition().getZ());
                            json.append("},");
                            json.append("\"displayText\":\"").append(escapeJson(content.getDisplayText())).append("\",");
                            json.append("\"backgroundColor\":").append(content.getBackgroundColor()).append(",");
                            json.append("\"textColor\":").append(content.getTextColor()).append(",");
                            json.append("\"textScale\":").append(content.getTextScale()).append(",");
                            json.append("\"multiblockWidth\":").append(content.getMultiblockWidth()).append(",");
                            json.append("\"multiblockHeight\":").append(content.getMultiblockHeight()).append(",");
                            json.append("\"isUnifiedScreen\":").append(content.isUnifiedScreen());

                            // Ajouter les données d'image si elles existent
                            if (content.getImageData() != null) {
                                String base64Image = Base64.getEncoder().encodeToString(content.getImageData());
                                json.append(",\"imageData\":\"").append(base64Image).append("\"");
                            }

                            json.append("}");
                            json.append("}");

                            return json.toString();
                        }
                    }
                }

                return "{\"status\":\"error\",\"message\":\"Session non trouvée\"}";
            } catch (Exception e) {
                ScreenBlock.LOGGER.error("Erreur lors de la récupération des données d'écran", e);
                return "{\"status\":\"error\",\"message\":\"Erreur serveur\"}";
            }
        }

        private String handleUpdateScreen(HttpExchange exchange) throws IOException {
            try {
                // Lire le corps de la requête
                String requestBody = new String(exchange.getRequestBody().readAllBytes());

                // Parser les données JSON (simple parsing)
                if (requestBody.contains("sessionId") && requestBody.contains("content")) {
                    String sessionId = extractJsonValue(requestBody, "sessionId");
                    String displayText = extractJsonValue(requestBody, "displayText");
                    String backgroundColorStr = extractJsonValue(requestBody, "backgroundColor");
                    String textColorStr = extractJsonValue(requestBody, "textColor");
                    String textScaleStr = extractJsonValue(requestBody, "textScale");

                    ScreenEditSession session = editSessions.get(sessionId);
                    if (session != null) {
                        World world = getServerWorld();
                        if (world != null && world.getBlockEntity(session.getPosition()) instanceof ScreenDisplayBlockEntity entity) {
                            ScreenContent content = entity.getContent();

                            // Mettre à jour le contenu
                            if (displayText != null) content.setDisplayText(displayText);
                            if (backgroundColorStr != null) {
                                try {
                                    content.setBackgroundColor(Integer.parseInt(backgroundColorStr));
                                } catch (NumberFormatException ignored) {}
                            }
                            if (textColorStr != null) {
                                try {
                                    content.setTextColor(Integer.parseInt(textColorStr));
                                } catch (NumberFormatException ignored) {}
                            }
                            if (textScaleStr != null) {
                                try {
                                    content.setTextScale(Float.parseFloat(textScaleStr));
                                } catch (NumberFormatException ignored) {}
                            }

                            // Appliquer les changements
                            entity.setContent(content);

                            // Synchroniser avec le réseau si on est côté serveur
                            if (!world.isClient) {
                                ScreenNetworkHandler.syncToAllClients((net.minecraft.server.world.ServerWorld) world, session.getPosition(), content);
                            }

                            return "{\"status\":\"saved\",\"message\":\"Écran mis à jour avec succès\"}";
                        }
                    }
                }

                return "{\"status\":\"error\",\"message\":\"Données invalides\"}";
            } catch (Exception e) {
                ScreenBlock.LOGGER.error("Erreur lors de la mise à jour de l'écran", e);
                return "{\"status\":\"error\",\"message\":\"Erreur serveur\"}";
            }
        }

        private String extractJsonValue(String json, String key) {
            try {
                String searchKey = "\"" + key + "\":";
                int startIndex = json.indexOf(searchKey);
                if (startIndex == -1) return null;

                startIndex += searchKey.length();

                // Ignorer les espaces
                while (startIndex < json.length() && Character.isWhitespace(json.charAt(startIndex))) {
                    startIndex++;
                }

                if (startIndex >= json.length()) return null;

                char firstChar = json.charAt(startIndex);
                if (firstChar == '"') {
                    // Valeur string
                    startIndex++;
                    int endIndex = json.indexOf('"', startIndex);
                    if (endIndex == -1) return null;
                    return json.substring(startIndex, endIndex);
                } else {
                    // Valeur numérique ou booléenne
                    int endIndex = startIndex;
                    while (endIndex < json.length() &&
                           json.charAt(endIndex) != ',' &&
                           json.charAt(endIndex) != '}' &&
                           json.charAt(endIndex) != ']') {
                        endIndex++;
                    }
                    return json.substring(startIndex, endIndex).trim();
                }
            } catch (Exception e) {
                return null;
            }
        }

        private String escapeJson(String str) {
            if (str == null) return "";
            return str.replace("\\", "\\\\")
                     .replace("\"", "\\\"")
                     .replace("\n", "\\n")
                     .replace("\r", "\\r")
                     .replace("\t", "\\t");
        }
    }

    // Handler pour l'upload d'images
    static class ImageUploadHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("POST".equals(exchange.getRequestMethod())) {
                // Traiter l'upload d'image
                String response = "{\"status\":\"uploaded\",\"url\":\"/images/uploaded.png\"}";

                exchange.getResponseHeaders().set("Content-Type", "application/json");
                exchange.sendResponseHeaders(200, response.getBytes().length);

                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(response.getBytes());
                }
            }
        }
    }

    // Handler pour sauvegarder
    static class SaveHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("POST".equals(exchange.getRequestMethod())) {
                try {
                    // Lire les données JSON
                    String requestBody = readRequestBody(exchange);

                    // Parser les données (simple parsing JSON)
                    String sessionId = extractJsonValue(requestBody, "sessionId");
                    String imageData = extractJsonValue(requestBody, "imageData");

                    if (sessionId != null && imageData != null) {
                        // Appliquer la texture dans le jeu avec synchronisation réseau
                        boolean success = applyTextureToGameWithSync(sessionId, imageData);

                        String response = success ?
                            "{\"status\":\"saved\",\"message\":\"Texture appliquée et synchronisée avec succès !\"}" :
                            "{\"status\":\"error\",\"message\":\"Erreur lors de l'application de la texture\"}";

                        // Ajouter les headers CORS
                        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
                        exchange.getResponseHeaders().set("Content-Type", "application/json");
                        exchange.sendResponseHeaders(200, response.getBytes().length);

                        try (OutputStream os = exchange.getResponseBody()) {
                            os.write(response.getBytes());
                        }
                    } else {
                        sendErrorResponse(exchange, "Données manquantes");
                    }
                } catch (Exception e) {
                    ScreenBlock.LOGGER.error("Erreur lors de la sauvegarde", e);
                    sendErrorResponse(exchange, "Erreur serveur: " + e.getMessage());
                }
            }
        }

        private String readRequestBody(HttpExchange exchange) throws IOException {
            StringBuilder body = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }
            }
            return body.toString();
        }

        private String extractJsonValue(String json, String key) {
            // Simple JSON parsing pour extraire une valeur
            String searchKey = "\"" + key + "\":\"";
            int startIndex = json.indexOf(searchKey);
            if (startIndex == -1) return null;

            startIndex += searchKey.length();
            int endIndex = json.indexOf("\"", startIndex);
            if (endIndex == -1) return null;

            return json.substring(startIndex, endIndex);
        }

        private void sendErrorResponse(HttpExchange exchange, String message) throws IOException {
            String response = "{\"status\":\"error\",\"message\":\"" + message + "\"}";
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.sendResponseHeaders(500, response.getBytes().length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }

    // Handler pour la page de l'éditeur
    static class EditorPageHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String sessionId = getQueryParam(exchange.getRequestURI().getQuery(), "session");

            // Charger la page de l'éditeur (sera créée dans le prochain fichier)
            String response = getEditorHTML(sessionId);

            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }

        private String getQueryParam(String query, String param) {
            if (query == null) return null;

            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2 && keyValue[0].equals(param)) {
                    return keyValue[1];
                }
            }
            return null;
        }

        private String getEditorHTML(String sessionId) {
            return EditorHTML.generateEditorPage(sessionId);
        }
    }

    // Classe pour gérer les sessions d'édition
    static class ScreenEditSession {
        private final BlockPos position;
        private ScreenContent content;
        private long lastAccess;

        public ScreenEditSession(BlockPos position) {
            this.position = position;
            this.lastAccess = System.currentTimeMillis();

            // Charger le contenu actuel du bloc
            loadCurrentContent();
        }

        private void loadCurrentContent() {
            World world = getServerWorld();
            if (world != null) {
                if (world.getBlockEntity(position) instanceof ScreenDisplayBlockEntity entity) {
                    this.content = entity.getContent();
                }
            }

            if (this.content == null) {
                this.content = new ScreenContent();
            }
        }

        public BlockPos getPosition() { return position; }
        public ScreenContent getContent() { return content; }
        public void setContent(ScreenContent content) {
            this.content = content;
            this.lastAccess = System.currentTimeMillis();
        }
        public long getLastAccess() { return lastAccess; }
    }

    /**
     * Applique la texture dans le jeu Minecraft
     */
    private static boolean applyTextureToGame(String sessionId, String imageDataUrl) {
        try {
            // Récupérer la session d'édition
            ScreenEditSession session = editSessions.get(sessionId);
            if (session == null) {
                ScreenBlock.LOGGER.error("Session non trouvée: " + sessionId);
                return false;
            }

            // Décoder l'image base64
            byte[] imageData = decodeBase64Image(imageDataUrl);
            if (imageData == null) {
                ScreenBlock.LOGGER.error("Impossible de décoder l'image");
                return false;
            }

            // Exécuter sur le thread principal de Minecraft
            World world = getServerWorld();
            if (world == null) {
                ScreenBlock.LOGGER.error("Monde Minecraft non disponible");
                return false;
            }

            // Programmer l'exécution sur le thread principal avec synchronisation réseau
            if (minecraftServer != null) {
                minecraftServer.execute(() -> {
                    try {
                        applyTextureToBlocksWithSync(world, session.getPosition(), imageData);
                    } catch (Exception e) {
                        ScreenBlock.LOGGER.error("Erreur lors de l'application de la texture", e);
                    }
                });
            }

            return true;

        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur dans applyTextureToGame", e);
            return false;
        }
    }

    /**
     * Applique une texture dans le jeu Minecraft avec synchronisation réseau
     */
    private static boolean applyTextureToGameWithSync(String sessionId, String imageDataUrl) {
        try {
            // Récupérer la session d'édition
            ScreenEditSession session = editSessions.get(sessionId);
            if (session == null) {
                ScreenBlock.LOGGER.error("Session non trouvée: " + sessionId);
                return false;
            }

            // Décoder l'image base64
            byte[] imageData = decodeBase64Image(imageDataUrl);
            if (imageData == null) {
                ScreenBlock.LOGGER.error("Impossible de décoder l'image");
                return false;
            }

            // Exécuter sur le thread principal de Minecraft
            World world = getServerWorld();
            if (world == null) {
                ScreenBlock.LOGGER.error("Monde Minecraft non disponible");
                return false;
            }

            // Programmer l'exécution sur le thread principal avec synchronisation réseau
            if (minecraftServer != null) {
                minecraftServer.execute(() -> {
                    try {
                        applyTextureToBlocksWithSync(world, session.getPosition(), imageData);
                    } catch (Exception e) {
                        ScreenBlock.LOGGER.error("Erreur lors de l'application de la texture avec sync", e);
                    }
                });
            }

            return true;

        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur dans applyTextureToGameWithSync", e);
            return false;
        }
    }

    /**
     * Décode une image base64 en bytes
     */
    private static byte[] decodeBase64Image(String imageDataUrl) {
        try {
            // Supprimer le préfixe "data:image/png;base64,"
            String base64Data = imageDataUrl;
            if (imageDataUrl.contains(",")) {
                base64Data = imageDataUrl.split(",")[1];
            }

            return Base64.getDecoder().decode(base64Data);

        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur lors du décodage base64", e);
            return null;
        }
    }

    /**
     * Applique la texture aux blocs dans le monde
     */
    private static void applyTextureToBlocks(World world, BlockPos startPos, byte[] imageData) {
        try {
            ScreenBlock.LOGGER.info("Début de l'application de texture à partir de " + startPos);

            // Vérifier si le bloc fait partie d'un écran unifié
            if (AutoUnifiedScreenManager.isPartOfUnifiedScreen(world, startPos)) {
                ScreenBlock.LOGGER.info("Bloc fait partie d'un écran unifié, application via le gestionnaire automatique");

                // Créer le contenu avec l'image
                ScreenContent content = new ScreenContent();
                content.setImageData(imageData);

                // Le gestionnaire automatique se chargera de la distribution
                AutoUnifiedScreenManager.updateUnifiedScreenContent(world, startPos, content);

            } else {
                // Détecter l'écran unifié manuellement
                UnifiedScreenManager.UnifiedScreen unifiedScreen =
                    UnifiedScreenManager.detectUnifiedScreen(world, startPos);

                if (unifiedScreen != null && unifiedScreen.getBlocks().size() > 1) {
                    ScreenBlock.LOGGER.info("Écran unifié détecté: " +
                        unifiedScreen.getWidth() + "x" + unifiedScreen.getHeight() + " blocs (" +
                        unifiedScreen.getBlocks().size() + " blocs au total)");

                    // Appliquer l'image globale à l'écran unifié
                    unifiedScreen.setGlobalImageData(imageData);

                    // Synchroniser tous les blocs
                    int blocksUpdated = 0;
                    for (BlockPos pos : unifiedScreen.getBlocks()) {
                        if (world.getBlockEntity(pos) instanceof ScreenDisplayBlockEntity entity) {
                            ScreenContent content = entity.getContent();
                            if (content != null) {
                                entity.setContent(content);
                                entity.setDirty();
                                blocksUpdated++;
                            }
                        }
                    }

                    ScreenBlock.LOGGER.info("Écran unifié mis à jour avec succès ! " + blocksUpdated + " blocs mis à jour.");

                } else {
                    // Fallback vers un seul bloc
                    ScreenBlock.LOGGER.info("Aucun écran unifié détecté, application sur bloc unique");

                    if (world.getBlockEntity(startPos) instanceof ScreenDisplayBlockEntity entity) {
                        ScreenContent content = entity.getContent();
                        if (content == null) {
                            content = new ScreenContent();
                        }

                        content.setImageData(imageData);
                        content.setMultiblockPosition(0, 0);
                        content.setMultiblockSize(1, 1);

                        entity.setContent(content);
                        entity.setDirty();

                        ScreenBlock.LOGGER.info("Bloc unique mis à jour: " + startPos);
                    }
                }
            }

        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur lors de l'application de l'écran unifié", e);
            e.printStackTrace();
        }
    }

    /**
     * Applique la texture aux blocs dans le monde avec le nouveau système dynamique
     */
    private static void applyTextureToBlocksWithSync(World world, BlockPos startPos, byte[] imageData) {
        try {
            ScreenBlock.LOGGER.info("Application de texture avec nouveau système dynamique à partir de " + startPos);

            // Créer le contenu avec l'image
            ScreenContent content = new ScreenContent();
            content.setImageData(imageData);

            // Utiliser le nouveau système dynamique qui gère automatiquement :
            // - La détection des structures connectées
            // - La distribution intelligente du contenu
            // - La synchronisation réseau
            // - L'adaptation aux rotations et tailles
            DynamicScreenDisplaySystem.updateDynamicScreenContent(world, startPos, content);

            ScreenBlock.LOGGER.info("Contenu mis à jour avec le système dynamique pour " + startPos);

        } catch (Exception e) {
            ScreenBlock.LOGGER.error("Erreur lors de l'application avec le système dynamique", e);
            e.printStackTrace();
        }
    }

    /**
     * Enhanced API handler for dynamic screen system
     */
    static class EnhancedHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String method = exchange.getRequestMethod();
            String path = exchange.getRequestURI().getPath();
            String query = exchange.getRequestURI().getQuery();

            try {
                String response;

                if ("GET".equals(method)) {
                    if (path.endsWith("/session")) {
                        response = handleGetSession(query);
                    } else if (path.endsWith("/info")) {
                        response = handleGetInfo(query);
                    } else {
                        response = "{\"error\":\"Unknown GET endpoint\"}";
                    }
                } else if ("POST".equals(method)) {
                    if (path.endsWith("/create")) {
                        response = handleCreateSession(exchange);
                    } else if (path.endsWith("/update")) {
                        response = handleUpdateContent(exchange);
                    } else {
                        response = "{\"error\":\"Unknown POST endpoint\"}";
                    }
                } else {
                    response = "{\"error\":\"Method not allowed\"}";
                }

                // Send response
                exchange.getResponseHeaders().set("Content-Type", "application/json");
                exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
                exchange.sendResponseHeaders(200, response.getBytes().length);

                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(response.getBytes());
                }

            } catch (Exception e) {
                ScreenBlock.LOGGER.error("Error in enhanced handler", e);
                String errorResponse = "{\"error\":\"Server error: " + e.getMessage() + "\"}";
                exchange.sendResponseHeaders(500, errorResponse.getBytes().length);

                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(errorResponse.getBytes());
                }
            }
        }

        private String handleGetSession(String query) {
            if (query == null) return "{\"error\":\"Missing session ID\"}";

            String sessionId = null;
            for (String param : query.split("&")) {
                String[] parts = param.split("=");
                if (parts.length == 2 && "sessionId".equals(parts[0])) {
                    sessionId = parts[1];
                    break;
                }
            }

            if (sessionId == null) {
                return "{\"error\":\"Missing session ID parameter\"}";
            }

            return DynamicWebInterface.getSessionInfo(sessionId);
        }

        private String handleGetInfo(String query) {
            return DynamicWebInterface.getSessionStats();
        }

        private String handleCreateSession(HttpExchange exchange) throws IOException {
            // Parse request body for position
            String body = new String(exchange.getRequestBody().readAllBytes());

            // Simple JSON parsing for position
            try {
                int x = extractIntFromJson(body, "x");
                int y = extractIntFromJson(body, "y");
                int z = extractIntFromJson(body, "z");

                BlockPos pos = new BlockPos(x, y, z);

                // Get world (this would need proper world access)
                World world = getServerWorld();
                if (world == null) {
                    return "{\"error\":\"World not available\"}";
                }

                String sessionId = DynamicWebInterface.createEnhancedSession(world, pos);
                if (sessionId != null) {
                    return "{\"status\":\"success\",\"sessionId\":\"" + sessionId + "\"}";
                } else {
                    return "{\"error\":\"Failed to create session\"}";
                }

            } catch (Exception e) {
                return "{\"error\":\"Invalid request format\"}";
            }
        }

        private String handleUpdateContent(HttpExchange exchange) throws IOException {
            String body = new String(exchange.getRequestBody().readAllBytes());

            try {
                String sessionId = extractStringFromJson(body, "sessionId");
                String contentType = extractStringFromJson(body, "contentType");
                String data = extractStringFromJson(body, "data");

                // Extract parameters
                Map<String, String> parameters = new HashMap<>();
                // Add parameter extraction logic here if needed

                return DynamicWebInterface.updateEnhancedContent(sessionId, contentType, data, parameters);

            } catch (Exception e) {
                return "{\"error\":\"Invalid request format: " + e.getMessage() + "\"}";
            }
        }

        private int extractIntFromJson(String json, String key) {
            String pattern = "\"" + key + "\"\\s*:\\s*(\\d+)";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return Integer.parseInt(m.group(1));
            }
            throw new IllegalArgumentException("Key not found: " + key);
        }

        private String extractStringFromJson(String json, String key) {
            String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]+)\"";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return m.group(1);
            }
            throw new IllegalArgumentException("Key not found: " + key);
        }
    }

    // New Dynamic Editor Handlers

    // Handler for the dynamic editor page
    static class DynamicEditorPageHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String sessionId = getQueryParam(exchange.getRequestURI().getQuery(), "session");

            if (sessionId == null) {
                sendErrorResponse(exchange, "Session ID required");
                return;
            }

            // Generate dynamic editor HTML
            String response = DynamicEditorHTML.generateDynamicEditorPage(sessionId);

            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }

        private String getQueryParam(String query, String param) {
            if (query == null) return null;

            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2 && keyValue[0].equals(param)) {
                    return keyValue[1];
                }
            }
            return null;
        }

        private void sendErrorResponse(HttpExchange exchange, String message) throws IOException {
            String response = "<html><body><h1>Error</h1><p>" + message + "</p></body></html>";
            exchange.getResponseHeaders().set("Content-Type", "text/html");
            exchange.sendResponseHeaders(400, response.getBytes().length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }

    // Handler for session information
    static class SessionInfoHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            addCorsHeaders(exchange);

            if ("OPTIONS".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(200, 0);
                exchange.getResponseBody().close();
                return;
            }

            if ("POST".equals(exchange.getRequestMethod())) {
                try {
                    String requestBody = readRequestBody(exchange);
                    String sessionId = extractStringFromJson(requestBody, "sessionId");

                    String response = DynamicWebInterface.getSessionInfo(sessionId);

                    exchange.getResponseHeaders().set("Content-Type", "application/json");
                    exchange.sendResponseHeaders(200, response.getBytes().length);

                    try (OutputStream os = exchange.getResponseBody()) {
                        os.write(response.getBytes());
                    }

                } catch (Exception e) {
                    ScreenBlock.LOGGER.error("Error handling session info request", e);
                    sendJsonErrorResponse(exchange, "Error retrieving session info: " + e.getMessage());
                }
            }
        }
    }

    // Handler for structure change checking
    static class StructureChangeHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            addCorsHeaders(exchange);

            if ("OPTIONS".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(200, 0);
                exchange.getResponseBody().close();
                return;
            }

            if ("POST".equals(exchange.getRequestMethod())) {
                try {
                    String requestBody = readRequestBody(exchange);
                    String sessionId = extractStringFromJson(requestBody, "sessionId");

                    String response = DynamicWebInterface.checkForStructureChanges(sessionId);

                    exchange.getResponseHeaders().set("Content-Type", "application/json");
                    exchange.sendResponseHeaders(200, response.getBytes().length);

                    try (OutputStream os = exchange.getResponseBody()) {
                        os.write(response.getBytes());
                    }

                } catch (Exception e) {
                    ScreenBlock.LOGGER.error("Error checking structure changes", e);
                    sendJsonErrorResponse(exchange, "Error checking structure changes: " + e.getMessage());
                }
            }
        }
    }

    // Handler for content updates
    static class UpdateContentHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            addCorsHeaders(exchange);

            if ("OPTIONS".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(200, 0);
                exchange.getResponseBody().close();
                return;
            }

            if ("POST".equals(exchange.getRequestMethod())) {
                try {
                    String requestBody = readRequestBody(exchange);
                    String sessionId = extractStringFromJson(requestBody, "sessionId");
                    String contentType = extractStringFromJson(requestBody, "contentType");
                    String data = extractStringFromJson(requestBody, "data");

                    // Create parameters map (can be extended for additional parameters)
                    Map<String, String> parameters = new HashMap<>();

                    String response = DynamicWebInterface.updateEnhancedContent(sessionId, contentType, data, parameters);

                    exchange.getResponseHeaders().set("Content-Type", "application/json");
                    exchange.sendResponseHeaders(200, response.getBytes().length);

                    try (OutputStream os = exchange.getResponseBody()) {
                        os.write(response.getBytes());
                    }

                } catch (Exception e) {
                    ScreenBlock.LOGGER.error("Error updating content", e);
                    sendJsonErrorResponse(exchange, "Error updating content: " + e.getMessage());
                }
            }
        }
    }

    // Helper methods for new handlers

    private static void addCorsHeaders(HttpExchange exchange) {
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");
    }

    private static String readRequestBody(HttpExchange exchange) throws IOException {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        }
        return body.toString();
    }

    private static void sendJsonErrorResponse(HttpExchange exchange, String message) throws IOException {
        String response = "{\"error\":\"" + message.replace("\"", "\\\"") + "\"}";
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        exchange.sendResponseHeaders(500, response.getBytes().length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(response.getBytes());
        }
    }

    private static String extractStringFromJson(String json, String key) {
        String pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(json);
        if (m.find()) {
            return m.group(1);
        }
        throw new IllegalArgumentException("Key not found: " + key);
    }

}
